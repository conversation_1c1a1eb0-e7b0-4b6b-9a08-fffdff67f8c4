#ifndef SLUB_H
#define SLUB_H

#include <stdint.h>
#include <stddef.h>
#include <stdbool.h>
#include <pthread.h>

/* 自旋锁定义 */
typedef struct {
    volatile int lock;
} spinlock_t;

#define SPINLOCK_INIT {0}

/* 页大小定义 */
#define PAGE_SIZE 4096
#define PAGE_SHIFT 12
#define PAGE_MASK (~(PAGE_SIZE - 1))

/* 最大CPU数量 */
#define MAX_CPUS 64

/* 最大页数量（模拟物理内存） */
#define MAX_PAGES 65536

/* SLUB分配器的对象大小范围 */
#define MIN_OBJECT_SIZE 8
#define MAX_OBJECT_SIZE 2048  /* 最大对象大小为页大小的一半 */

/* 每页最大对象数量 */
#define MAX_OBJECTS_PER_PAGE (PAGE_SIZE / MIN_OBJECT_SIZE)

/* 页标志位 */
#define PG_slab     0x01    /* 页属于SLUB分配器 */
#define PG_active   0x02    /* 页正在使用中 */

/* 前向声明 */
struct kmem_cache;
struct page;

/**
 * 页结构体 - 模拟内核中的struct page
 * 用于管理物理页的元数据
 */
struct page {
    unsigned long flags;           /* 页标志位 */
    struct kmem_cache *slab_cache; /* 指向所属的kmem_cache */
    void *freelist;               /* 空闲对象链表头 */
    unsigned int inuse;           /* 已使用对象数量 */
    unsigned int objects;         /* 总对象数量 */
    struct page *next;            /* 链表中的下一页 */
    void *s_mem;                  /* 页的虚拟地址 */
    uint64_t page_id;            /* 页ID（用于调试） */
};

/**
 * Per-CPU缓存结构体
 * 每个CPU都有独立的缓存，减少锁竞争
 */
struct kmem_cache_cpu {
    void **freelist;              /* 当前CPU的空闲对象链表 */
    struct page *page;            /* 当前正在使用的页 */
    struct page *partial;         /* 部分使用的页链表 */
    unsigned long tid;            /* 事务ID，用于无锁操作 */
    spinlock_t lock;              /* CPU缓存自旋锁 */
    int cpu_id;                   /* 绑定的CPU ID */
};

/**
 * SLUB缓存结构体 - 核心数据结构
 * 管理特定大小对象的分配
 */
struct kmem_cache {
    struct kmem_cache_cpu __percpu[MAX_CPUS]; /* Per-CPU缓存数组 */
    
    /* 缓存属性 */
    unsigned int size;            /* 对象大小 */
    unsigned int object_size;     /* 实际对象大小（不包括元数据） */
    unsigned int offset;          /* 空闲指针偏移 */
    unsigned int objects_per_page; /* 每页对象数量 */
    
    /* 页管理 */
    struct page *partial_list;    /* 部分使用的页链表 */
    spinlock_t list_lock;         /* 页链表自旋锁 */
    
    /* 统计信息 */
    unsigned long alloc_count;    /* 分配次数 */
    unsigned long free_count;     /* 释放次数 */
    unsigned long page_count;     /* 使用的页数 */
    
    /* 缓存标识 */
    char name[32];               /* 缓存名称 */
    unsigned int flags;          /* 缓存标志 */
    
    /* 链表节点 */
    struct kmem_cache *next;     /* 全局缓存链表 */
};

/**
 * 页分配器结构体
 * 模拟内核页分配器
 */
struct page_allocator {
    struct page pages[MAX_PAGES]; /* 页元素区数组 */
    void *memory_pool;            /* mmap分配的内存池 */
    unsigned long *bitmap;        /* 页使用位图 */
    unsigned long total_pages;    /* 总页数 */
    unsigned long free_pages;     /* 空闲页数 */
    spinlock_t lock;              /* 页分配器自旋锁 */
};

/**
 * SLUB分配器全局状态
 */
struct slub_allocator {
    struct page_allocator page_alloc; /* 页分配器 */
    struct kmem_cache *cache_list;    /* 缓存链表头 */
    spinlock_t cache_lock;            /* 缓存链表自旋锁 */
    bool initialized;                 /* 初始化标志 */
};

/* 全局分配器实例 */
extern struct slub_allocator g_slub;

/* 预定义的通用缓存大小 */
extern struct kmem_cache *kmalloc_caches[12]; /* 8, 16, 32, ..., 2048 */

/**
 * 函数声明
 */

/* 初始化和清理 */
int slub_init(void);
void slub_cleanup(void);

/* 页分配器接口 */
struct page *alloc_page(void);
void free_page(struct page *page);
void *page_to_virt(struct page *page);
struct page *virt_to_page(void *addr);

/* 缓存管理 */
struct kmem_cache *kmem_cache_create(const char *name, size_t size, 
                                   size_t align, unsigned long flags);
void kmem_cache_destroy(struct kmem_cache *cache);
void *kmem_cache_alloc(struct kmem_cache *cache);
void kmem_cache_free(struct kmem_cache *cache, void *obj);

/* 通用内存分配 */
void *kmalloc(size_t size);
void kfree(void *ptr);

/* 工具函数 */
int get_cpu_id(void);           /* 获取CPU ID（真实+模拟） */
int get_current_cpu_id(void);   /* 获取当前CPU ID（不刷新缓存） */
int get_cpu_id_refresh(void);   /* 强制重新获取CPU ID */
void set_cpu_id(int cpu_id);    /* 手动设置CPU ID（测试用） */
size_t kmem_cache_size(struct kmem_cache *cache);
void slub_print_stats(void);

/* 自旋锁函数 */
void spin_lock_init(spinlock_t *lock);
void spin_lock(spinlock_t *lock);
void spin_unlock(spinlock_t *lock);
int spin_trylock(spinlock_t *lock);

/* CPU支持函数 */
int init_percpu_cache(struct kmem_cache_cpu *cpu_cache);
void cleanup_percpu_cache(struct kmem_cache_cpu *cpu_cache);
void *percpu_cache_alloc_fast(struct kmem_cache *cache);
bool percpu_cache_free_fast(struct kmem_cache *cache, void *object);
int get_system_cpu_count(void);
void set_thread_affinity(int cpu_id);

/* 初始化函数 */
bool slub_is_initialized(void);
void slub_get_info(int *total_caches, int *total_pages, size_t *total_memory);
void slub_gc(void);

/* 调试函数 */
void slub_debug_print_cache(struct kmem_cache *cache);
void slub_debug_print_page(struct page *page);
void slub_print_fragmentation(void);
bool slub_verify_integrity(void);
void slub_export_debug_info(const char *filename);

#endif /* SLUB_H */
