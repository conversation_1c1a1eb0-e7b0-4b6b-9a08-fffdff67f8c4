#include "slub.h"
#include <stdlib.h>
#include <time.h>
#include <stdio.h>
#include <unistd.h>
#include <pthread.h>

/* 线程本地存储，用于缓存CPU ID */
static __thread int cached_cpu_id = -1;
static __thread bool cpu_id_initialized = false;

/* 全局CPU计数器，用于负载均衡 */
static volatile int global_cpu_counter = 0;
static pthread_mutex_t cpu_counter_lock = PTHREAD_MUTEX_INITIALIZER;

/* 随机数生成器状态 */
static bool random_initialized = false;
static pthread_mutex_t random_lock = PTHREAD_MUTEX_INITIALIZER;

/**
 * 初始化随机数生成器
 * 使用当前时间和进程ID作为种子
 */
static void init_random(void) {
    pthread_mutex_lock(&random_lock);
    if (!random_initialized) {
        unsigned int seed = (unsigned int)(time(NULL) ^ getpid());
        srand(seed);
        random_initialized = true;
        printf("[CPU_SUPPORT] 随机数生成器初始化，种子: %u\n", seed);
    }
    pthread_mutex_unlock(&random_lock);
}

/**
 * 获取CPU ID - 使用随机数模拟
 * 
 * 在真实的内核中，这通常通过以下方式实现：
 * - x86: 读取APIC ID
 * - ARM: 读取MPIDR寄存器
 * - 或使用per-CPU变量
 * 
 * 这里我们使用随机数模拟，但会在线程生命周期内保持一致
 */
int get_cpu_id(void) {
    /* 如果已经为当前线程缓存了CPU ID，直接返回 */
    if (cpu_id_initialized) {
        return cached_cpu_id;
    }
    
    /* 初始化随机数生成器 */
    if (!random_initialized) {
        init_random();
    }
    
    /* 生成CPU ID的几种策略 */
    int cpu_id;
    
    /* 策略1: 纯随机 - 模拟真实的多核调度 */
    cpu_id = rand() % MAX_CPUS;
    
    /* 策略2: 基于线程ID的哈希 - 提供更好的一致性 */
    pthread_t tid = pthread_self();
    uintptr_t tid_val = (uintptr_t)tid;
    int hash_cpu = (tid_val ^ (tid_val >> 16)) % MAX_CPUS;
    
    /* 策略3: 轮询分配 - 确保负载均衡 */
    pthread_mutex_lock(&cpu_counter_lock);
    int round_robin_cpu = global_cpu_counter % MAX_CPUS;
    global_cpu_counter++;
    pthread_mutex_unlock(&cpu_counter_lock);
    
    /* 混合策略：优先使用哈希，但加入一些随机性 */
    if (rand() % 100 < 80) {  /* 80%的概率使用哈希 */
        cpu_id = hash_cpu;
    } else if (rand() % 100 < 50) {  /* 10%的概率使用轮询 */
        cpu_id = round_robin_cpu;
    }
    /* 否则使用纯随机（10%概率） */
    
    /* 缓存CPU ID到线程本地存储 */
    cached_cpu_id = cpu_id;
    cpu_id_initialized = true;
    
    printf("[CPU_SUPPORT] 线程 %lu 分配到CPU %d\n", 
           (unsigned long)pthread_self(), cpu_id);
    
    return cpu_id;
}

/**
 * 强制重新获取CPU ID
 * 模拟进程迁移到不同CPU的情况
 */
int get_cpu_id_refresh(void) {
    cpu_id_initialized = false;
    return get_cpu_id();
}

/**
 * 获取当前CPU ID（不刷新缓存）
 */
int get_current_cpu_id(void) {
    if (cpu_id_initialized) {
        return cached_cpu_id;
    }
    return get_cpu_id();
}

/**
 * 初始化per-CPU缓存
 */
int init_percpu_cache(struct kmem_cache_cpu *cpu_cache) {
    if (!cpu_cache) return -1;
    
    /* 初始化CPU缓存结构 */
    cpu_cache->freelist = NULL;
    cpu_cache->page = NULL;
    cpu_cache->partial = NULL;
    cpu_cache->tid = 0;
    
    /* 初始化锁 */
    if (pthread_mutex_init(&cpu_cache->lock, NULL) != 0) {
        return -1;
    }
    
    return 0;
}

/**
 * 清理per-CPU缓存
 */
void cleanup_percpu_cache(struct kmem_cache_cpu *cpu_cache) {
    if (!cpu_cache) return;
    
    pthread_mutex_destroy(&cpu_cache->lock);
    
    /* 清理缓存内容 */
    cpu_cache->freelist = NULL;
    cpu_cache->page = NULL;
    cpu_cache->partial = NULL;
    cpu_cache->tid = 0;
}

/**
 * 无锁快速路径 - 尝试从CPU缓存分配对象
 * 
 * 这是SLUB分配器的核心优化：
 * 1. 首先尝试从当前CPU的freelist分配
 * 2. 如果freelist为空，尝试从当前页重新填充
 * 3. 如果都失败，返回NULL，调用者需要走慢速路径
 */
void *percpu_cache_alloc_fast(struct kmem_cache *cache) {
    int cpu_id = get_current_cpu_id();
    struct kmem_cache_cpu *cpu_cache = &cache->__percpu[cpu_id];
    void *object = NULL;
    
    /* 
     * 快速路径：尝试无锁分配
     * 在真实内核中，这里会使用更复杂的无锁算法，
     * 比如cmpxchg指令来保证原子性
     */
    
    /* 简化的无锁检查 */
    if (cpu_cache->freelist) {
        /* 尝试获取锁，如果失败则放弃快速路径 */
        if (pthread_mutex_trylock(&cpu_cache->lock) == 0) {
            /* 双重检查 */
            if (cpu_cache->freelist) {
                object = cpu_cache->freelist;
                /* 更新freelist指针 */
                cpu_cache->freelist = *(void **)object;
                cpu_cache->tid++;  /* 增加事务ID */
            }
            pthread_mutex_unlock(&cpu_cache->lock);
        }
    }
    
    return object;
}

/**
 * 无锁快速路径 - 尝试释放对象到CPU缓存
 */
bool percpu_cache_free_fast(struct kmem_cache *cache, void *object) {
    int cpu_id = get_current_cpu_id();
    struct kmem_cache_cpu *cpu_cache = &cache->__percpu[cpu_id];
    bool success = false;
    
    /* 尝试获取锁，如果失败则放弃快速路径 */
    if (pthread_mutex_trylock(&cpu_cache->lock) == 0) {
        /* 将对象添加到freelist头部 */
        *(void **)object = cpu_cache->freelist;
        cpu_cache->freelist = object;
        cpu_cache->tid++;  /* 增加事务ID */
        success = true;
        
        pthread_mutex_unlock(&cpu_cache->lock);
    }
    
    return success;
}

/**
 * 获取CPU缓存统计信息
 */
void get_percpu_stats(struct kmem_cache *cache, int cpu_id, 
                     int *freelist_count, bool *has_page) {
    if (cpu_id < 0 || cpu_id >= MAX_CPUS) return;
    
    struct kmem_cache_cpu *cpu_cache = &cache->__percpu[cpu_id];
    
    pthread_mutex_lock(&cpu_cache->lock);
    
    /* 计算freelist中的对象数量 */
    *freelist_count = 0;
    void *obj = cpu_cache->freelist;
    while (obj && *freelist_count < 1000) {  /* 防止无限循环 */
        obj = *(void **)obj;
        (*freelist_count)++;
    }
    
    *has_page = (cpu_cache->page != NULL);
    
    pthread_mutex_unlock(&cpu_cache->lock);
}

/**
 * 打印CPU支持统计信息
 */
void cpu_support_print_stats(void) {
    printf("\n=== CPU支持统计信息 ===\n");
    printf("最大CPU数量: %d\n", MAX_CPUS);
    printf("当前线程CPU ID: %d\n", get_current_cpu_id());
    printf("全局CPU计数器: %d\n", global_cpu_counter);
    printf("随机数生成器状态: %s\n", random_initialized ? "已初始化" : "未初始化");
}
