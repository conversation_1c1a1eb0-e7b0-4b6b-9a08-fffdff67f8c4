#include "slub.h"
#include <stdlib.h>
#include <time.h>
#include <stdio.h>
#include <unistd.h>
#include <pthread.h>
#include <sched.h>
#include <errno.h>

#ifdef __linux__
#include <sys/syscall.h>
#endif

#ifdef __APPLE__
#include <sys/sysctl.h>
#include <mach/thread_policy.h>
#include <mach/thread_act.h>
#include <mach/mach.h>
#endif

/* 线程本地存储，用于缓存CPU ID */
static __thread int cached_cpu_id = -1;
static __thread bool cpu_id_initialized = false;

/* 全局CPU计数器，用于负载均衡 */
static volatile int global_cpu_counter = 0;

/* 自旋锁实现已在头文件中声明，这里提供实现 */

/* 全局自旋锁 */
static spinlock_t cpu_counter_lock = SPINLOCK_INIT;
static spinlock_t random_lock = SPINLOCK_INIT;

/* 随机数生成器状态 */
static bool random_initialized = false;

/**
 * 自旋锁函数实现
 */
void spin_lock_init(spinlock_t *lock) {
    lock->lock = 0;
}

void spin_lock(spinlock_t *lock) {
    while (__sync_lock_test_and_set(&lock->lock, 1)) {
        /* 自旋等待，使用CPU pause指令减少功耗 */
        #ifdef __x86_64__
        __asm__ __volatile__("pause" ::: "memory");
        #elif defined(__aarch64__)
        __asm__ __volatile__("yield" ::: "memory");
        #else
        /* 其他架构使用短暂延迟 */
        for (volatile int i = 0; i < 10; i++);
        #endif
    }
}

void spin_unlock(spinlock_t *lock) {
    __sync_lock_release(&lock->lock);
}

int spin_trylock(spinlock_t *lock) {
    return !__sync_lock_test_and_set(&lock->lock, 1);
}

/**
 * 获取系统CPU数量
 */
int get_system_cpu_count(void) {
    static int cpu_count = -1;
    if (cpu_count != -1) {
        return cpu_count;
    }

#ifdef __linux__
    cpu_count = sysconf(_SC_NPROCESSORS_ONLN);
#elif defined(__APPLE__)
    size_t size = sizeof(cpu_count);
    if (sysctlbyname("hw.ncpu", &cpu_count, &size, NULL, 0) != 0) {
        cpu_count = 1;
    }
#else
    cpu_count = 1;  /* 默认单核 */
#endif

    if (cpu_count <= 0) cpu_count = 1;
    if (cpu_count > MAX_CPUS) cpu_count = MAX_CPUS;

    printf("[CPU_SUPPORT] 检测到系统CPU数量: %d\n", cpu_count);
    return cpu_count;
}

/**
 * 设置线程CPU亲和性
 */
void set_thread_affinity(int cpu_id) {
    if (cpu_id < 0 || cpu_id >= get_system_cpu_count()) {
        printf("[CPU_SUPPORT] 无效的CPU ID: %d\n", cpu_id);
        return;
    }

#ifdef __linux__
    cpu_set_t cpuset;
    CPU_ZERO(&cpuset);
    CPU_SET(cpu_id, &cpuset);

    if (pthread_setaffinity_np(pthread_self(), sizeof(cpu_set_t), &cpuset) == 0) {
        printf("[CPU_SUPPORT] 线程 %lu 成功绑定到CPU %d\n",
               (unsigned long)pthread_self(), cpu_id);
    } else {
        printf("[CPU_SUPPORT] 线程 %lu 绑定到CPU %d 失败: %s\n",
               (unsigned long)pthread_self(), cpu_id, strerror(errno));
    }

#elif defined(__APPLE__)
    /* macOS 使用 thread affinity policy */
    thread_affinity_policy_data_t policy;
    policy.affinity_tag = cpu_id;

    if (thread_policy_set(mach_thread_self(), THREAD_AFFINITY_POLICY,
                         (thread_policy_t)&policy, THREAD_AFFINITY_POLICY_COUNT) == KERN_SUCCESS) {
        printf("[CPU_SUPPORT] 线程 %lu 成功设置亲和性到CPU %d\n",
               (unsigned long)pthread_self(), cpu_id);
    } else {
        printf("[CPU_SUPPORT] 线程 %lu 设置亲和性到CPU %d 失败\n",
               (unsigned long)pthread_self(), cpu_id);
    }
#else
    printf("[CPU_SUPPORT] 当前平台不支持CPU亲和性设置\n");
#endif
}

/**
 * 获取真实的CPU ID
 */
static int get_real_cpu_id(void) {
#ifdef __linux__
    /* Linux: 使用sched_getcpu() */
    int cpu = sched_getcpu();
    if (cpu >= 0) {
        return cpu % MAX_CPUS;
    }

    /* 备选方案：读取/proc/stat */
    FILE *fp = fopen("/proc/stat", "r");
    if (fp) {
        /* 这里可以解析CPU统计信息，但比较复杂 */
        fclose(fp);
    }

#elif defined(__APPLE__)
    /* macOS: 使用thread affinity */
    thread_port_t thread = mach_thread_self();
    thread_affinity_policy_data_t policy;
    mach_msg_type_number_t count = THREAD_AFFINITY_POLICY_COUNT;
    boolean_t get_default = FALSE;

    if (thread_policy_get(thread, THREAD_AFFINITY_POLICY,
                         (thread_policy_t)&policy, &count, &get_default) == KERN_SUCCESS) {
        return policy.affinity_tag % MAX_CPUS;
    }

    /* 备选方案：使用系统调用获取当前处理器 */
    int cpu = 0;
    size_t size = sizeof(cpu);
    if (sysctlbyname("machdep.cpu.core_count", &cpu, &size, NULL, 0) == 0) {
        /* 这不是真正的当前CPU，但可以作为备选 */
        uintptr_t tid = (uintptr_t)pthread_self();
        return (int)(tid % cpu % MAX_CPUS);
    }
#endif

    /* 如果无法获取真实CPU ID，使用线程ID的哈希 */
    uintptr_t tid = (uintptr_t)pthread_self();
    return (int)((tid ^ (tid >> 16)) % MAX_CPUS);
}

/* init_random函数已移除，因为现在主要使用真实CPU ID */

/**
 * 获取CPU ID - 优先使用真实CPU ID
 *
 * 在真实的内核中，这通常通过以下方式实现：
 * - x86: 读取APIC ID
 * - ARM: 读取MPIDR寄存器
 * - 或使用per-CPU变量
 *
 * 这里我们优先获取真实的CPU ID，如果失败则使用模拟策略
 */
int get_cpu_id(void) {
    /* 如果已经为当前线程缓存了CPU ID，直接返回 */
    if (cpu_id_initialized) {
        return cached_cpu_id;
    }

    int cpu_id;
    int system_cpus = get_system_cpu_count();

    /* 策略1: 尝试获取真实的CPU ID */
    cpu_id = get_real_cpu_id();

    /* 验证CPU ID的有效性 */
    if (cpu_id < 0 || cpu_id >= MAX_CPUS) {
        /* 策略2: 基于线程ID的哈希 - 提供一致性 */
        pthread_t tid = pthread_self();
        uintptr_t tid_val = (uintptr_t)tid;
        cpu_id = (tid_val ^ (tid_val >> 16)) % system_cpus;

        /* 策略3: 轮询分配 - 确保负载均衡 */
        spin_lock(&cpu_counter_lock);
        int round_robin_cpu = global_cpu_counter % system_cpus;
        global_cpu_counter++;
        spin_unlock(&cpu_counter_lock);

        /* 在哈希和轮询之间选择 */
        if ((tid_val & 1) == 0) {
            cpu_id = round_robin_cpu;
        }
    }

    /* 确保CPU ID在有效范围内 */
    cpu_id = cpu_id % MAX_CPUS;

    /* 缓存CPU ID到线程本地存储 */
    cached_cpu_id = cpu_id;
    cpu_id_initialized = true;

    printf("[CPU_SUPPORT] 线程 %lu 绑定到CPU %d (系统CPU数: %d)\n",
           (unsigned long)pthread_self(), cpu_id, system_cpus);

    return cpu_id;
}

/**
 * 强制重新获取CPU ID
 * 模拟进程迁移到不同CPU的情况
 */
int get_cpu_id_refresh(void) {
    cpu_id_initialized = false;
    return get_cpu_id();
}

/**
 * 获取当前CPU ID（不刷新缓存）
 */
int get_current_cpu_id(void) {
    if (cpu_id_initialized) {
        return cached_cpu_id;
    }
    return get_cpu_id();
}

/**
 * 初始化per-CPU缓存
 */
int init_percpu_cache(struct kmem_cache_cpu *cpu_cache) {
    if (!cpu_cache) return -1;

    /* 初始化CPU缓存结构 */
    cpu_cache->freelist = NULL;
    cpu_cache->page = NULL;
    cpu_cache->partial = NULL;
    cpu_cache->tid = 0;
    cpu_cache->cpu_id = -1;  /* 初始化为无效CPU ID */

    /* 初始化自旋锁 */
    spin_lock_init(&cpu_cache->lock);

    return 0;
}

/**
 * 清理per-CPU缓存
 */
void cleanup_percpu_cache(struct kmem_cache_cpu *cpu_cache) {
    if (!cpu_cache) return;

    /* 自旋锁不需要显式销毁 */

    /* 清理缓存内容 */
    cpu_cache->freelist = NULL;
    cpu_cache->page = NULL;
    cpu_cache->partial = NULL;
    cpu_cache->tid = 0;
    cpu_cache->cpu_id = -1;
}

/**
 * 快速路径 - 尝试从CPU缓存分配对象
 *
 * 这是SLUB分配器的核心优化：
 * 1. 首先尝试从当前CPU的freelist分配
 * 2. 如果freelist为空，尝试从当前页重新填充
 * 3. 如果都失败，返回NULL，调用者需要走慢速路径
 */
void *percpu_cache_alloc_fast(struct kmem_cache *cache) {
    int cpu_id = get_current_cpu_id();
    struct kmem_cache_cpu *cpu_cache = &cache->__percpu[cpu_id];
    void *object = NULL;

    /* 记录CPU ID到缓存中 */
    if (cpu_cache->cpu_id != cpu_id) {
        cpu_cache->cpu_id = cpu_id;
    }

    /*
     * 快速路径：尝试使用自旋锁的快速分配
     * 使用trylock避免在快速路径中阻塞
     */

    /* 尝试获取自旋锁，如果失败则放弃快速路径 */
    if (spin_trylock(&cpu_cache->lock)) {
        /* 检查freelist是否有可用对象 */
        if (cpu_cache->freelist) {
            object = cpu_cache->freelist;
            /* 更新freelist指针 */
            cpu_cache->freelist = *(void **)object;
            cpu_cache->tid++;  /* 增加事务ID */
        }
        spin_unlock(&cpu_cache->lock);
    }

    return object;
}

/**
 * 快速路径 - 尝试释放对象到CPU缓存
 */
bool percpu_cache_free_fast(struct kmem_cache *cache, void *object) {
    int cpu_id = get_current_cpu_id();
    struct kmem_cache_cpu *cpu_cache = &cache->__percpu[cpu_id];
    bool success = false;

    /* 记录CPU ID到缓存中 */
    if (cpu_cache->cpu_id != cpu_id) {
        cpu_cache->cpu_id = cpu_id;
    }

    /* 尝试获取自旋锁，如果失败则放弃快速路径 */
    if (spin_trylock(&cpu_cache->lock)) {
        /* 将对象添加到freelist头部 */
        *(void **)object = cpu_cache->freelist;
        cpu_cache->freelist = object;
        cpu_cache->tid++;  /* 增加事务ID */
        success = true;

        spin_unlock(&cpu_cache->lock);
    }

    return success;
}

/**
 * 获取CPU缓存统计信息
 */
void get_percpu_stats(struct kmem_cache *cache, int cpu_id,
                     int *freelist_count, bool *has_page) {
    if (cpu_id < 0 || cpu_id >= MAX_CPUS) return;

    struct kmem_cache_cpu *cpu_cache = &cache->__percpu[cpu_id];

    spin_lock(&cpu_cache->lock);

    /* 计算freelist中的对象数量 */
    *freelist_count = 0;
    void *obj = cpu_cache->freelist;
    while (obj && *freelist_count < 1000) {  /* 防止无限循环 */
        obj = *(void **)obj;
        (*freelist_count)++;
    }

    *has_page = (cpu_cache->page != NULL);

    spin_unlock(&cpu_cache->lock);
}

/**
 * 打印CPU支持统计信息
 */
void cpu_support_print_stats(void) {
    printf("\n=== CPU支持统计信息 ===\n");
    printf("最大CPU数量: %d\n", MAX_CPUS);
    printf("当前线程CPU ID: %d\n", get_current_cpu_id());
    printf("全局CPU计数器: %d\n", global_cpu_counter);
    printf("随机数生成器状态: %s\n", random_initialized ? "已初始化" : "未初始化");
}
