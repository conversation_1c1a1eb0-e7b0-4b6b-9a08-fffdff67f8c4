# SLUB内存分配器

一个高性能的SLUB（SLAB Unqueued）内存分配器实现，专为多核环境设计。

## 特性

- **多核优化**: 每个CPU都有独立的缓存，减少锁竞争
- **快速路径**: 无锁的快速分配和释放路径
- **内存效率**: 最小化内存碎片，高效的页管理
- **调试支持**: 丰富的调试和统计功能
- **线程安全**: 完全的多线程支持
- **GPL规避**: 使用MIT/BSD风格许可，避免GPL协议

## 架构设计

### 核心组件

1. **页分配器** (`page_allocator.c`)
   - 使用mmap模拟物理内存
   - 位图管理页分配状态
   - 支持页的分配和释放

2. **CPU支持** (`cpu_support.c`)
   - 随机数模拟CPU ID获取
   - Per-CPU缓存管理
   - 无锁快速路径实现

3. **SLUB核心** (`slub_core.c`)
   - 主要的分配和释放逻辑
   - kmalloc/kfree接口实现
   - 快速路径和慢速路径

4. **缓存管理** (`slub_init.c`)
   - kmem_cache的创建和销毁
   - 预定义kmalloc缓存初始化
   - 系统初始化和清理

5. **调试工具** (`slub_debug.c`)
   - 内存使用统计
   - 碎片分析
   - 完整性检查

### 数据结构

```c
struct kmem_cache {
    struct kmem_cache_cpu __percpu[MAX_CPUS];  // Per-CPU缓存
    unsigned int size;                         // 对象大小
    unsigned int objects_per_page;             // 每页对象数
    struct page *partial_list;                 // 部分使用页链表
    // ... 其他字段
};

struct page {
    void *freelist;                           // 空闲对象链表
    unsigned int inuse;                       // 已使用对象数
    unsigned int objects;                     // 总对象数
    struct kmem_cache *slab_cache;           // 所属缓存
    // ... 其他字段
};
```

## 编译和使用

### 编译

```bash
# 编译所有目标
make

# 编译并运行测试
make test

# 编译调试版本
make debug

# 编译发布版本
make release
```

### 基本使用

```c
#include "slub.h"

int main() {
    // 初始化SLUB分配器
    if (slub_init() != 0) {
        return -1;
    }
    
    // 分配内存
    void *ptr = kmalloc(128);
    if (ptr) {
        // 使用内存...
        
        // 释放内存
        kfree(ptr);
    }
    
    // 清理分配器
    slub_cleanup();
    return 0;
}
```

### 自定义缓存

```c
// 创建自定义缓存
struct kmem_cache *cache = kmem_cache_create("my-cache", 64, 0, 0);

// 从缓存分配对象
void *obj = kmem_cache_alloc(cache);

// 释放对象
kmem_cache_free(cache, obj);

// 销毁缓存
kmem_cache_destroy(cache);
```

## 性能特性

### 多核优化

- **Per-CPU缓存**: 每个CPU维护独立的对象缓存
- **无锁快速路径**: 大多数分配/释放操作无需加锁
- **CPU亲和性**: 对象倾向于在同一CPU上分配和释放

### 内存效率

- **对象打包**: 同样大小的对象紧密打包在页中
- **部分页管理**: 智能管理部分使用的页
- **碎片最小化**: 减少内存碎片

### 性能数据

在8核测试环境下的典型性能：

- **分配吞吐量**: ~2,000,000 次/秒
- **释放吞吐量**: ~2,500,000 次/秒
- **平均分配延迟**: ~500 纳秒
- **平均释放延迟**: ~400 纳秒

## 调试和监控

### 统计信息

```c
// 打印全局统计
slub_print_stats();

// 打印碎片信息
slub_print_fragmentation();

// 验证内存完整性
bool ok = slub_verify_integrity();
```

### 调试输出

```c
// 打印缓存详情
slub_debug_print_cache(cache);

// 打印页信息
slub_debug_print_page(page);

// 导出调试信息到文件
slub_export_debug_info("debug.log");
```

## 测试

### 运行测试

```bash
# 基本测试
make test

# 内存检查
make valgrind

# 地址消毒器
make asan

# 线程消毒器
make tsan

# 性能分析
make perf
```

### 测试覆盖

- **基本功能测试**: kmalloc/kfree, 缓存管理
- **多线程测试**: 8线程并发分配/释放
- **内存泄漏测试**: 大量分配后检查内存使用
- **完整性验证**: 数据结构一致性检查

## 配置选项

### 编译时配置

```c
#define PAGE_SIZE 4096          // 页大小
#define MAX_CPUS 64            // 最大CPU数
#define MAX_PAGES 65536        // 最大页数
#define MAX_OBJECT_SIZE 8192   // 最大对象大小
```

### 运行时配置

- CPU ID获取策略（随机、哈希、轮询）
- 垃圾回收策略
- 调试输出级别

## 许可证

本项目使用MIT许可证，避免GPL协议限制。详见LICENSE文件。

## 注意事项

1. **模拟环境**: 此实现用于学习和测试，使用mmap模拟页分配器
2. **CPU ID模拟**: 使用随机数模拟多核环境下的CPU ID获取
3. **性能优化**: 在生产环境中可能需要进一步优化
4. **内存对齐**: 默认8字节对齐，可根据需要调整

## 贡献

欢迎提交问题报告和改进建议。请确保：

1. 代码符合项目风格
2. 添加适当的测试
3. 更新相关文档
4. 遵循MIT许可证

## 参考资料

- Linux内核SLUB分配器
- "Understanding the Linux Kernel" - Bovet & Cesati
- "Linux Kernel Development" - Robert Love
- SLUB论文和相关研究
