#include "slub.h"
#include <sys/mman.h>
#include <unistd.h>
#include <string.h>
#include <stdio.h>
#include <stdlib.h>
#include <errno.h>

/* 全局页分配器 */
static struct page_allocator *g_page_alloc = NULL;

/**
 * 初始化页分配器
 * 使用mmap分配大块内存模拟物理内存
 */
int page_allocator_init(struct page_allocator *alloc) {
    size_t total_size = MAX_PAGES * PAGE_SIZE;
    size_t bitmap_size = (MAX_PAGES + 63) / 64 * sizeof(unsigned long);
    
    printf("[PAGE_ALLOC] 初始化页分配器，总内存: %zu MB\n", 
           total_size / (1024 * 1024));
    
    /* 使用mmap分配内存池 */
    alloc->memory_pool = mmap(NULL, total_size, 
                             PROT_READ | PROT_WRITE,
                             MAP_PRIVATE | MAP_ANONYMOUS, -1, 0);
    if (alloc->memory_pool == MAP_FAILED) {
        perror("mmap failed");
        return -1;
    }
    
    /* 分配位图 */
    alloc->bitmap = malloc(bitmap_size);
    if (!alloc->bitmap) {
        munmap(alloc->memory_pool, total_size);
        return -1;
    }
    
    /* 初始化位图（全部标记为空闲） */
    memset(alloc->bitmap, 0, bitmap_size);
    
    /* 初始化页结构数组 */
    for (int i = 0; i < MAX_PAGES; i++) {
        struct page *page = &alloc->pages[i];
        memset(page, 0, sizeof(struct page));
        page->page_id = i;
        page->s_mem = (char *)alloc->memory_pool + i * PAGE_SIZE;
    }
    
    /* 初始化统计信息 */
    alloc->total_pages = MAX_PAGES;
    alloc->free_pages = MAX_PAGES;
    
    /* 初始化自旋锁 */
    spin_lock_init(&alloc->lock);
    
    g_page_alloc = alloc;
    printf("[PAGE_ALLOC] 页分配器初始化成功，可用页数: %lu\n", alloc->free_pages);
    return 0;
}

/**
 * 清理页分配器
 */
void page_allocator_cleanup(struct page_allocator *alloc) {
    if (!alloc) return;
    
    printf("[PAGE_ALLOC] 清理页分配器，已使用页数: %lu\n", 
           alloc->total_pages - alloc->free_pages);
    
    /* 自旋锁不需要显式销毁 */
    
    if (alloc->bitmap) {
        free(alloc->bitmap);
        alloc->bitmap = NULL;
    }
    
    if (alloc->memory_pool && alloc->memory_pool != MAP_FAILED) {
        munmap(alloc->memory_pool, MAX_PAGES * PAGE_SIZE);
        alloc->memory_pool = NULL;
    }
    
    g_page_alloc = NULL;
}

/**
 * 在位图中查找空闲页
 */
static int find_free_page_bit(struct page_allocator *alloc) {
    size_t bitmap_longs = (MAX_PAGES + 63) / 64;
    
    for (size_t i = 0; i < bitmap_longs; i++) {
        unsigned long word = alloc->bitmap[i];
        if (word != ~0UL) {  /* 如果这个long不是全1，说明有空闲位 */
            /* 找到第一个0位 */
            for (int bit = 0; bit < 64 && (i * 64 + bit) < MAX_PAGES; bit++) {
                if (!(word & (1UL << bit))) {
                    return i * 64 + bit;
                }
            }
        }
    }
    return -1;  /* 没有找到空闲页 */
}

/**
 * 设置位图中的位
 */
static void set_page_bit(struct page_allocator *alloc, int page_idx) {
    int word_idx = page_idx / 64;
    int bit_idx = page_idx % 64;
    alloc->bitmap[word_idx] |= (1UL << bit_idx);
}

/**
 * 清除位图中的位
 */
static void clear_page_bit(struct page_allocator *alloc, int page_idx) {
    int word_idx = page_idx / 64;
    int bit_idx = page_idx % 64;
    alloc->bitmap[word_idx] &= ~(1UL << bit_idx);
}

/**
 * 分配一页内存
 * 返回页结构指针，失败返回NULL
 */
struct page *alloc_page(void) {
    if (!g_page_alloc) {
        printf("[PAGE_ALLOC] 错误：页分配器未初始化\n");
        return NULL;
    }
    
    struct page_allocator *alloc = g_page_alloc;
    struct page *page = NULL;
    
    spin_lock(&alloc->lock);
    
    /* 检查是否有空闲页 */
    if (alloc->free_pages == 0) {
        printf("[PAGE_ALLOC] 警告：内存不足，无法分配页\n");
        goto out;
    }
    
    /* 查找空闲页 */
    int page_idx = find_free_page_bit(alloc);
    if (page_idx < 0) {
        printf("[PAGE_ALLOC] 错误：位图查找失败\n");
        goto out;
    }
    
    /* 标记页为已使用 */
    set_page_bit(alloc, page_idx);
    alloc->free_pages--;
    
    /* 获取页结构并初始化 */
    page = &alloc->pages[page_idx];
    memset(page, 0, sizeof(struct page));
    page->page_id = page_idx;
    page->s_mem = (char *)alloc->memory_pool + page_idx * PAGE_SIZE;
    page->flags = PG_active;
    
    printf("[PAGE_ALLOC] 分配页 %d，剩余空闲页: %lu\n",
           page_idx, alloc->free_pages);

out:
    spin_unlock(&alloc->lock);
    return page;
}

/**
 * 释放一页内存
 */
void free_page(struct page *page) {
    if (!page || !g_page_alloc) {
        printf("[PAGE_ALLOC] 错误：无效的页指针或分配器未初始化\n");
        return;
    }
    
    struct page_allocator *alloc = g_page_alloc;
    int page_idx = page->page_id;
    
    /* 验证页索引有效性 */
    if (page_idx < 0 || page_idx >= MAX_PAGES) {
        printf("[PAGE_ALLOC] 错误：无效的页索引 %d\n", page_idx);
        return;
    }
    
    spin_lock(&alloc->lock);

    /* 清除页标记 */
    clear_page_bit(alloc, page_idx);
    alloc->free_pages++;

    /* 清理页结构 */
    memset(page, 0, sizeof(struct page));
    page->page_id = page_idx;
    page->s_mem = (char *)alloc->memory_pool + page_idx * PAGE_SIZE;

    printf("[PAGE_ALLOC] 释放页 %d，当前空闲页: %lu\n",
           page_idx, alloc->free_pages);

    spin_unlock(&alloc->lock);
}

/**
 * 页结构转虚拟地址
 */
void *page_to_virt(struct page *page) {
    if (!page) return NULL;
    return page->s_mem;
}

/**
 * 虚拟地址转页结构
 */
struct page *virt_to_page(void *addr) {
    if (!addr || !g_page_alloc) return NULL;
    
    struct page_allocator *alloc = g_page_alloc;
    char *base = (char *)alloc->memory_pool;
    char *ptr = (char *)addr;
    
    /* 检查地址是否在内存池范围内 */
    if (ptr < base || ptr >= base + MAX_PAGES * PAGE_SIZE) {
        return NULL;
    }
    
    /* 计算页索引 */
    int page_idx = (ptr - base) / PAGE_SIZE;
    if (page_idx < 0 || page_idx >= MAX_PAGES) {
        return NULL;
    }
    
    return &alloc->pages[page_idx];
}

/**
 * 打印页分配器统计信息
 */
void page_allocator_print_stats(void) {
    if (!g_page_alloc) {
        printf("[PAGE_ALLOC] 页分配器未初始化\n");
        return;
    }
    
    struct page_allocator *alloc = g_page_alloc;
    spin_lock(&alloc->lock);

    printf("\n=== 页分配器统计信息 ===\n");
    printf("总页数: %lu\n", alloc->total_pages);
    printf("空闲页数: %lu\n", alloc->free_pages);
    printf("已使用页数: %lu\n", alloc->total_pages - alloc->free_pages);
    printf("内存使用率: %.2f%%\n",
           (double)(alloc->total_pages - alloc->free_pages) * 100.0 / alloc->total_pages);
    printf("总内存: %lu MB\n", alloc->total_pages * PAGE_SIZE / (1024 * 1024));
    printf("已使用内存: %lu MB\n",
           (alloc->total_pages - alloc->free_pages) * PAGE_SIZE / (1024 * 1024));

    spin_unlock(&alloc->lock);
}
