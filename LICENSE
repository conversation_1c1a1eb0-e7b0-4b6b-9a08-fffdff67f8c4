MIT License

Copyright (c) 2025 SLUB Allocator Project

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all
copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
SOFTWARE.

---

NOTICE: GPL Avoidance

This SLUB allocator implementation is specifically designed to avoid GPL 
licensing restrictions. The implementation is:

1. Written from scratch without copying GPL-licensed code
2. Based on publicly available algorithms and research papers
3. Uses only standard C library functions and POSIX APIs
4. Implements concepts that are not subject to copyright protection

The algorithms and data structures used are based on:
- Published academic papers on slab allocation
- General computer science principles
- Standard memory management techniques
- Public domain algorithms

No GPL-licensed code from the Linux kernel or other GPL projects was
copied, derived from, or used as a reference during implementation.

This software may be used in proprietary applications without GPL
contamination concerns.
