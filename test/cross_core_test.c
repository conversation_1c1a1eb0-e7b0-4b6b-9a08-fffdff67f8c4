/**
 * SLUB分配器跨核内存分配释放测试程序
 * 
 * 测试内存异核释放的情况：
 * - 在一个CPU核心上分配内存
 * - 在另一个CPU核心上释放内存
 * - 验证SLUB分配器的跨核安全性
 */

#include "slub.h"
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <pthread.h>
#include <time.h>
#include <unistd.h>
#include <assert.h>

/* macOS不支持pthread_barrier，实现一个简单的替代方案 */
typedef struct {
    pthread_mutex_t mutex;
    pthread_cond_t cond;
    int count;
    int waiting;
} pthread_barrier_t;

static int pthread_barrier_init(pthread_barrier_t *barrier, void *attr, int count) {
    (void)attr;
    barrier->count = count;
    barrier->waiting = 0;
    pthread_mutex_init(&barrier->mutex, NULL);
    pthread_cond_init(&barrier->cond, NULL);
    return 0;
}

static int pthread_barrier_wait(pthread_barrier_t *barrier) {
    pthread_mutex_lock(&barrier->mutex);
    barrier->waiting++;
    if (barrier->waiting >= barrier->count) {
        barrier->waiting = 0;
        pthread_cond_broadcast(&barrier->cond);
        pthread_mutex_unlock(&barrier->mutex);
        return 1;
    } else {
        pthread_cond_wait(&barrier->cond, &barrier->mutex);
        pthread_mutex_unlock(&barrier->mutex);
        return 0;
    }
}

static int pthread_barrier_destroy(pthread_barrier_t *barrier) {
    pthread_mutex_destroy(&barrier->mutex);
    pthread_cond_destroy(&barrier->cond);
    return 0;
}

/* 测试配置 */
#define MAX_ALLOCATIONS 1000
#define MAX_THREADS 8
#define TEST_SIZES_COUNT 9

/* 测试大小数组 */
static const size_t test_sizes[TEST_SIZES_COUNT] = {
    8, 16, 32, 64, 128, 256, 512, 1024, 2048
};

/* 分配记录 */
typedef struct allocation_record {
    void *ptr;
    size_t size;
    int alloc_cpu;
    int free_cpu;
    unsigned char pattern;
    struct allocation_record *next;
} allocation_record_t;

/* 线程数据 */
typedef struct {
    int thread_id;
    int allocations;
    int frees;
    int errors;
    pthread_barrier_t *barrier;
    allocation_record_t **shared_allocations;
    pthread_mutex_t *shared_mutex;
} thread_data_t;

/* 全局统计 */
static volatile int total_allocations = 0;
static volatile int total_frees = 0;
static volatile int cross_core_frees = 0;
static volatile int same_core_frees = 0;
static volatile int errors = 0;
static pthread_mutex_t stats_mutex = PTHREAD_MUTEX_INITIALIZER;

/**
 * 更新统计信息
 */
static void update_stats(int alloc_delta, int free_delta, int cross_core_delta, 
                        int same_core_delta, int error_delta) {
    pthread_mutex_lock(&stats_mutex);
    total_allocations += alloc_delta;
    total_frees += free_delta;
    cross_core_frees += cross_core_delta;
    same_core_frees += same_core_delta;
    errors += error_delta;
    pthread_mutex_unlock(&stats_mutex);
}

/**
 * 验证内存内容
 */
static int verify_memory(void *ptr, size_t size, unsigned char pattern) {
    unsigned char *data = (unsigned char *)ptr;
    for (size_t i = 0; i < size; i++) {
        if (data[i] != pattern) {
            return 0;
        }
    }
    return 1;
}

/**
 * 分配器线程 - 只负责分配内存
 */
static void *allocator_thread(void *arg) {
    thread_data_t *data = (thread_data_t *)arg;
    int cpu_id = get_cpu_id();
    
    printf("[分配线程 %d] 开始运行，CPU: %d\n", data->thread_id, cpu_id);
    
    // 等待所有线程准备就绪
    pthread_barrier_wait(data->barrier);
    
    for (int i = 0; i < MAX_ALLOCATIONS / 2; i++) {
        // 随机选择分配大小
        size_t size = test_sizes[rand() % TEST_SIZES_COUNT];
        void *ptr = kmalloc(size);
        
        if (ptr) {
            // 创建分配记录
            allocation_record_t *record = malloc(sizeof(allocation_record_t));
            if (record) {
                record->ptr = ptr;
                record->size = size;
                record->alloc_cpu = cpu_id;
                record->free_cpu = -1;
                record->pattern = (unsigned char)(data->thread_id + i);
                record->next = NULL;
                
                // 写入测试模式
                memset(ptr, record->pattern, size);
                
                // 添加到共享列表
                pthread_mutex_lock(data->shared_mutex);
                record->next = *data->shared_allocations;
                *data->shared_allocations = record;
                pthread_mutex_unlock(data->shared_mutex);
                
                data->allocations++;
                update_stats(1, 0, 0, 0, 0);
                
                printf("[分配线程 %d] 分配 %zu 字节，CPU %d，地址: %p，模式: 0x%02X\n",
                       data->thread_id, size, cpu_id, ptr, record->pattern);
            } else {
                kfree(ptr);
                data->errors++;
                update_stats(0, 0, 0, 0, 1);
            }
        } else {
            data->errors++;
            update_stats(0, 0, 0, 0, 1);
        }
        
        // 短暂休眠，让其他线程有机会运行
        usleep(1000);
    }
    
    printf("[分配线程 %d] 完成，分配: %d，错误: %d\n", 
           data->thread_id, data->allocations, data->errors);
    
    return NULL;
}

/**
 * 释放器线程 - 只负责释放内存
 */
static void *deallocator_thread(void *arg) {
    thread_data_t *data = (thread_data_t *)arg;
    int cpu_id = get_cpu_id();
    
    printf("[释放线程 %d] 开始运行，CPU: %d\n", data->thread_id, cpu_id);
    
    // 等待所有线程准备就绪
    pthread_barrier_wait(data->barrier);
    
    // 等待一些分配完成
    usleep(10000);
    
    while (data->frees < MAX_ALLOCATIONS / 2) {
        allocation_record_t *record = NULL;
        
        // 从共享列表中取出一个分配记录
        pthread_mutex_lock(data->shared_mutex);
        if (*data->shared_allocations) {
            record = *data->shared_allocations;
            *data->shared_allocations = record->next;
        }
        pthread_mutex_unlock(data->shared_mutex);
        
        if (record) {
            // 验证内存内容
            if (verify_memory(record->ptr, record->size, record->pattern)) {
                // 释放内存
                kfree(record->ptr);
                
                record->free_cpu = cpu_id;
                data->frees++;
                
                // 统计跨核释放
                if (record->alloc_cpu != record->free_cpu) {
                    update_stats(0, 1, 1, 0, 0);
                    printf("[释放线程 %d] 跨核释放：分配CPU %d → 释放CPU %d，大小: %zu，地址: %p\n",
                           data->thread_id, record->alloc_cpu, record->free_cpu, 
                           record->size, record->ptr);
                } else {
                    update_stats(0, 1, 0, 1, 0);
                    printf("[释放线程 %d] 同核释放：CPU %d，大小: %zu，地址: %p\n",
                           data->thread_id, record->free_cpu, record->size, record->ptr);
                }
                
                free(record);
            } else {
                printf("[释放线程 %d] 内存验证失败！地址: %p，大小: %zu\n",
                       data->thread_id, record->ptr, record->size);
                data->errors++;
                update_stats(0, 0, 0, 0, 1);
                free(record);
            }
        } else {
            // 没有可释放的内存，短暂休眠
            usleep(5000);
        }
    }
    
    printf("[释放线程 %d] 完成，释放: %d，错误: %d\n", 
           data->thread_id, data->frees, data->errors);
    
    return NULL;
}

/**
 * 混合线程 - 既分配又释放
 */
static void *mixed_thread(void *arg) {
    thread_data_t *data = (thread_data_t *)arg;
    int cpu_id = get_cpu_id();
    
    printf("[混合线程 %d] 开始运行，CPU: %d\n", data->thread_id, cpu_id);
    
    // 等待所有线程准备就绪
    pthread_barrier_wait(data->barrier);
    
    for (int i = 0; i < MAX_ALLOCATIONS / 4; i++) {
        // 分配内存
        size_t size = test_sizes[rand() % TEST_SIZES_COUNT];
        void *ptr = kmalloc(size);
        
        if (ptr) {
            unsigned char pattern = (unsigned char)(data->thread_id + i);
            memset(ptr, pattern, size);
            
            data->allocations++;
            update_stats(1, 0, 0, 0, 0);
            
            printf("[混合线程 %d] 分配 %zu 字节，CPU %d，地址: %p\n",
                   data->thread_id, size, cpu_id, ptr);
            
            // 短暂持有内存
            usleep(rand() % 5000);
            
            // 验证并释放
            if (verify_memory(ptr, size, pattern)) {
                kfree(ptr);
                data->frees++;
                update_stats(0, 1, 0, 1, 0);  // 同核释放
                
                printf("[混合线程 %d] 释放 %zu 字节，CPU %d，地址: %p\n",
                       data->thread_id, size, cpu_id, ptr);
            } else {
                printf("[混合线程 %d] 内存验证失败！\n", data->thread_id);
                data->errors++;
                update_stats(0, 0, 0, 0, 1);
            }
        } else {
            data->errors++;
            update_stats(0, 0, 0, 0, 1);
        }
        
        // 尝试释放其他线程分配的内存
        pthread_mutex_lock(data->shared_mutex);
        if (*data->shared_allocations) {
            allocation_record_t *record = *data->shared_allocations;
            *data->shared_allocations = record->next;
            pthread_mutex_unlock(data->shared_mutex);
            
            if (verify_memory(record->ptr, record->size, record->pattern)) {
                kfree(record->ptr);
                
                if (record->alloc_cpu != cpu_id) {
                    update_stats(0, 1, 1, 0, 0);
                    printf("[混合线程 %d] 跨核释放：分配CPU %d → 释放CPU %d\n",
                           data->thread_id, record->alloc_cpu, cpu_id);
                } else {
                    update_stats(0, 1, 0, 1, 0);
                }
                
                data->frees++;
            } else {
                data->errors++;
                update_stats(0, 0, 0, 0, 1);
            }
            
            free(record);
        } else {
            pthread_mutex_unlock(data->shared_mutex);
        }
    }
    
    printf("[混合线程 %d] 完成，分配: %d，释放: %d，错误: %d\n", 
           data->thread_id, data->allocations, data->frees, data->errors);
    
    return NULL;
}

/**
 * 运行跨核测试
 */
static void run_cross_core_test(void) {
    printf("\n=== 跨核内存分配释放测试 ===\n");
    
    const int num_threads = 6;
    pthread_t threads[num_threads];
    thread_data_t thread_data[num_threads];
    pthread_barrier_t barrier;
    allocation_record_t *shared_allocations = NULL;
    pthread_mutex_t shared_mutex = PTHREAD_MUTEX_INITIALIZER;
    
    // 初始化屏障
    pthread_barrier_init(&barrier, NULL, num_threads);
    
    // 重置统计
    total_allocations = 0;
    total_frees = 0;
    cross_core_frees = 0;
    same_core_frees = 0;
    errors = 0;
    
    printf("启动 %d 个线程进行跨核测试...\n", num_threads);
    
    // 创建线程
    for (int i = 0; i < num_threads; i++) {
        thread_data[i].thread_id = i;
        thread_data[i].allocations = 0;
        thread_data[i].frees = 0;
        thread_data[i].errors = 0;
        thread_data[i].barrier = &barrier;
        thread_data[i].shared_allocations = &shared_allocations;
        thread_data[i].shared_mutex = &shared_mutex;
        
        void *(*thread_func)(void*) = NULL;
        
        if (i < 2) {
            // 前2个线程只分配
            thread_func = allocator_thread;
        } else if (i < 4) {
            // 中间2个线程只释放
            thread_func = deallocator_thread;
        } else {
            // 后2个线程混合操作
            thread_func = mixed_thread;
        }
        
        if (pthread_create(&threads[i], NULL, thread_func, &thread_data[i]) != 0) {
            printf("创建线程 %d 失败\n", i);
            return;
        }
    }
    
    // 等待所有线程完成
    for (int i = 0; i < num_threads; i++) {
        pthread_join(threads[i], NULL);
    }
    
    // 清理剩余的分配记录
    while (shared_allocations) {
        allocation_record_t *record = shared_allocations;
        shared_allocations = shared_allocations->next;
        kfree(record->ptr);
        free(record);
        total_frees++;
    }
    
    // 销毁屏障和互斥锁
    pthread_barrier_destroy(&barrier);
    pthread_mutex_destroy(&shared_mutex);
    
    // 打印结果
    printf("\n--- 跨核测试结果 ---\n");
    printf("总分配次数: %d\n", total_allocations);
    printf("总释放次数: %d\n", total_frees);
    printf("跨核释放次数: %d (%.1f%%)\n", 
           cross_core_frees, 
           total_frees > 0 ? (double)cross_core_frees / total_frees * 100.0 : 0.0);
    printf("同核释放次数: %d (%.1f%%)\n", 
           same_core_frees,
           total_frees > 0 ? (double)same_core_frees / total_frees * 100.0 : 0.0);
    printf("错误次数: %d\n", errors);
    
    if (errors == 0 && cross_core_frees > 0) {
        printf("🎉 跨核测试成功！SLUB分配器支持跨核内存释放\n");
    } else if (errors == 0) {
        printf("⚠️  测试完成，但没有发生跨核释放\n");
    } else {
        printf("❌ 测试失败，发现错误\n");
    }
}

/**
 * 主函数
 */
int main(void) {
    printf("SLUB分配器跨核内存分配释放测试程序\n");
    printf("=====================================\n\n");
    
    // 初始化随机数种子
    srand((unsigned int)time(NULL));
    
    // 初始化SLUB分配器
    printf("初始化SLUB分配器...\n");
    if (slub_init() != 0) {
        printf("SLUB分配器初始化失败\n");
        return 1;
    }
    printf("✓ SLUB分配器初始化成功\n");
    
    // 运行跨核测试
    run_cross_core_test();
    
    // 打印最终统计
    printf("\n");
    slub_print_stats();
    
    // 清理SLUB分配器
    printf("\n清理SLUB分配器...\n");
    slub_cleanup();
    
    printf("\n跨核测试完成！\n");
    return 0;
}
