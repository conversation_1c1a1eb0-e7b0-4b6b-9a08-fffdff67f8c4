#include "slub.h"
#include <stdio.h>
#include <string.h>
#include <unistd.h>

/**
 * SLUB内存分配器演示程序
 * 展示基本功能和多核特性
 */

void demo_basic_allocation(void) {
    printf("\n=== 基本内存分配演示 ===\n");
    
    // 分配不同大小的内存
    void *ptr1 = kmalloc(64);
    void *ptr2 = kmalloc(256);
    void *ptr3 = kmalloc(1024);
    
    printf("分配 64 字节: %p\n", ptr1);
    printf("分配 256 字节: %p\n", ptr2);
    printf("分配 1024 字节: %p\n", ptr3);
    
    // 写入数据
    if (ptr1) {
        strcpy((char*)ptr1, "Hello SLUB!");
        printf("写入数据到 64 字节块: %s\n", (char*)ptr1);
    }
    
    // 释放内存
    kfree(ptr1);
    kfree(ptr2);
    kfree(ptr3);
    
    printf("内存已释放\n");
}

void demo_custom_cache(void) {
    printf("\n=== 自定义缓存演示 ===\n");
    
    // 创建自定义缓存
    struct kmem_cache *my_cache = kmem_cache_create("demo-cache", 128, 0, 0);
    if (!my_cache) {
        printf("创建缓存失败\n");
        return;
    }
    
    printf("创建自定义缓存 'demo-cache'，对象大小: 128 字节\n");
    
    // 从缓存分配对象
    void *obj1 = kmem_cache_alloc(my_cache);
    void *obj2 = kmem_cache_alloc(my_cache);
    void *obj3 = kmem_cache_alloc(my_cache);
    
    printf("从缓存分配对象: %p, %p, %p\n", obj1, obj2, obj3);
    
    // 使用对象
    if (obj1) {
        strcpy((char*)obj1, "缓存对象1");
        printf("对象1内容: %s\n", (char*)obj1);
    }
    
    // 释放对象
    kmem_cache_free(my_cache, obj1);
    kmem_cache_free(my_cache, obj2);
    kmem_cache_free(my_cache, obj3);
    
    // 打印缓存详情
    slub_debug_print_cache(my_cache);
    
    // 销毁缓存
    kmem_cache_destroy(my_cache);
    printf("缓存已销毁\n");
}

void demo_performance(void) {
    printf("\n=== 性能演示 ===\n");
    
    const int count = 10000;
    void *ptrs[count];
    
    printf("分配 %d 个 256 字节的对象...\n", count);
    
    // 批量分配
    for (int i = 0; i < count; i++) {
        ptrs[i] = kmalloc(256);
        if (!ptrs[i]) {
            printf("分配失败在第 %d 个对象\n", i);
            break;
        }
    }
    
    printf("分配完成\n");
    
    // 批量释放
    for (int i = 0; i < count; i++) {
        if (ptrs[i]) {
            kfree(ptrs[i]);
        }
    }
    
    printf("释放完成\n");
}

void demo_multicore_simulation(void) {
    printf("\n=== 多核模拟演示 ===\n");
    
    // 模拟不同CPU上的分配
    printf("当前CPU ID: %d\n", get_cpu_id());
    
    void *ptr1 = kmalloc(512);
    printf("CPU %d 分配: %p\n", get_current_cpu_id(), ptr1);
    
    // 强制切换CPU（模拟）
    int new_cpu = get_cpu_id_refresh();
    printf("切换到CPU: %d\n", new_cpu);
    
    void *ptr2 = kmalloc(512);
    printf("CPU %d 分配: %p\n", get_current_cpu_id(), ptr2);
    
    kfree(ptr1);
    kfree(ptr2);
}

void demo_statistics(void) {
    printf("\n=== 统计信息演示 ===\n");
    
    // 分配一些内存
    void *ptrs[100];
    for (int i = 0; i < 100; i++) {
        ptrs[i] = kmalloc(64 + i * 8);  // 不同大小
    }
    
    // 显示统计信息
    slub_print_stats();
    
    // 显示碎片信息
    slub_print_fragmentation();
    
    // 释放内存
    for (int i = 0; i < 100; i++) {
        kfree(ptrs[i]);
    }
    
    printf("\n释放后的统计信息:\n");
    slub_print_stats();
}

int main(void) {
    printf("SLUB内存分配器演示程序\n");
    printf("======================\n");
    
    // 初始化SLUB分配器
    if (slub_init() != 0) {
        printf("SLUB分配器初始化失败\n");
        return 1;
    }
    
    printf("SLUB分配器初始化成功\n");
    
    // 运行各种演示
    demo_basic_allocation();
    demo_custom_cache();
    demo_performance();
    demo_multicore_simulation();
    demo_statistics();
    
    // 最终统计
    printf("\n=== 最终系统状态 ===\n");
    int total_caches, total_pages;
    size_t total_memory;
    slub_get_info(&total_caches, &total_pages, &total_memory);
    
    printf("总缓存数: %d\n", total_caches);
    printf("使用页数: %d\n", total_pages);
    printf("使用内存: %zu KB\n", total_memory / 1024);
    
    // 验证完整性
    if (slub_verify_integrity()) {
        printf("内存完整性验证通过 ✓\n");
    } else {
        printf("内存完整性验证失败 ✗\n");
    }
    
    // 清理
    slub_cleanup();
    printf("\nSLUB分配器已清理\n");
    
    printf("\n演示完成！\n");
    return 0;
}
