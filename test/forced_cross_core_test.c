/**
 * 强制跨核内存分配释放测试程序
 * 
 * 通过手动模拟不同CPU ID来测试跨核释放场景
 */

#include "slub.h"
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <pthread.h>
#include <time.h>
#include <unistd.h>

/* 测试配置 */
#define MAX_ALLOCATIONS 50
#define MAX_CORES 4

/* 分配记录 */
typedef struct {
    void *ptr;
    size_t size;
    int alloc_cpu;
    unsigned char pattern;
} allocation_record_t;

/* 全局分配记录 */
static allocation_record_t allocations[MAX_ALLOCATIONS];
static int allocation_count = 0;

/**
 * 模拟在指定CPU上分配内存
 */
static int allocate_on_cpu(int cpu_id, int count) {
    printf("\n=== 在CPU %d上分配内存 ===\n", cpu_id);
    
    // 手动设置CPU ID（模拟线程绑定到特定CPU）
    set_cpu_id(cpu_id);
    int actual_cpu = get_cpu_id();
    printf("当前CPU ID: %d\n", actual_cpu);
    
    int allocated = 0;
    for (int i = 0; i < count && allocation_count < MAX_ALLOCATIONS; i++) {
        size_t size = 128 + (i % 8) * 128;  // 128, 256, 384, ..., 1024字节
        void *ptr = kmalloc(size);
        
        if (ptr) {
            unsigned char pattern = (unsigned char)(cpu_id * 16 + i);
            memset(ptr, pattern, size);
            
            allocations[allocation_count].ptr = ptr;
            allocations[allocation_count].size = size;
            allocations[allocation_count].alloc_cpu = actual_cpu;
            allocations[allocation_count].pattern = pattern;
            
            printf("  [%d] 分配 %zu 字节，地址: %p，模式: 0x%02X\n", 
                   allocation_count, size, ptr, pattern);
            
            allocation_count++;
            allocated++;
        } else {
            printf("  分配 %zu 字节失败\n", size);
        }
    }
    
    printf("CPU %d 分配完成，成功分配: %d\n", cpu_id, allocated);
    return allocated;
}

/**
 * 模拟在指定CPU上释放内存
 */
static int free_on_cpu(int cpu_id, int start_idx, int count) {
    printf("\n=== 在CPU %d上释放内存 ===\n", cpu_id);
    
    // 手动设置CPU ID（模拟线程迁移到不同CPU）
    set_cpu_id(cpu_id);
    int actual_cpu = get_cpu_id();
    printf("当前CPU ID: %d\n", actual_cpu);
    
    int freed = 0;
    int cross_core_frees = 0;
    
    for (int i = start_idx; i < start_idx + count && i < allocation_count; i++) {
        if (allocations[i].ptr) {
            // 验证内存内容
            unsigned char *data = (unsigned char *)allocations[i].ptr;
            int valid = 1;
            for (size_t j = 0; j < allocations[i].size; j++) {
                if (data[j] != allocations[i].pattern) {
                    valid = 0;
                    break;
                }
            }
            
            if (valid) {
                kfree(allocations[i].ptr);
                
                if (allocations[i].alloc_cpu != actual_cpu) {
                    cross_core_frees++;
                    printf("  [%d] 🔄 跨核释放：分配CPU %d → 释放CPU %d，大小: %zu，地址: %p\n",
                           i, allocations[i].alloc_cpu, actual_cpu, 
                           allocations[i].size, allocations[i].ptr);
                } else {
                    printf("  [%d] ✓ 同核释放：CPU %d，大小: %zu，地址: %p\n",
                           i, actual_cpu, allocations[i].size, allocations[i].ptr);
                }
                
                allocations[i].ptr = NULL;  // 标记为已释放
                freed++;
            } else {
                printf("  [%d] ❌ 内存验证失败！大小: %zu，地址: %p\n",
                       i, allocations[i].size, allocations[i].ptr);
            }
        }
    }
    
    printf("CPU %d 释放完成，成功释放: %d，跨核释放: %d\n", 
           cpu_id, freed, cross_core_frees);
    return cross_core_frees;
}

/**
 * 交叉分配释放测试
 */
static void cross_allocation_test(void) {
    printf("\n=== 交叉分配释放测试 ===\n");
    
    allocation_count = 0;
    memset(allocations, 0, sizeof(allocations));
    
    int total_cross_core = 0;
    int allocations_per_cpu = MAX_ALLOCATIONS / MAX_CORES;
    
    // 在不同CPU上分配内存
    for (int cpu = 0; cpu < MAX_CORES; cpu++) {
        allocate_on_cpu(cpu, allocations_per_cpu);
    }
    
    printf("\n总分配数: %d\n", allocation_count);
    
    // 在不同CPU上释放内存（交叉释放）
    for (int cpu = 0; cpu < MAX_CORES; cpu++) {
        // 每个CPU释放其他CPU分配的内存
        int start_idx = ((cpu + 1) % MAX_CORES) * allocations_per_cpu;
        int cross_core = free_on_cpu(cpu, start_idx, allocations_per_cpu);
        total_cross_core += cross_core;
    }
    
    printf("\n--- 交叉测试结果 ---\n");
    printf("总跨核释放次数: %d\n", total_cross_core);
    
    if (total_cross_core > 0) {
        printf("🎉 跨核释放测试成功！SLUB分配器支持跨核内存释放\n");
    } else {
        printf("⚠️  没有发生跨核释放\n");
    }
}

/**
 * 循环分配释放测试
 */
static void round_robin_test(void) {
    printf("\n=== 循环分配释放测试 ===\n");
    
    allocation_count = 0;
    memset(allocations, 0, sizeof(allocations));
    
    // 循环在不同CPU上分配
    for (int i = 0; i < MAX_ALLOCATIONS; i++) {
        int cpu = i % MAX_CORES;
        set_cpu_id(cpu);
        
        size_t size = 256 + (i % 4) * 256;  // 256, 512, 768, 1024字节
        void *ptr = kmalloc(size);
        
        if (ptr) {
            unsigned char pattern = (unsigned char)(i + 0x80);
            memset(ptr, pattern, size);
            
            allocations[allocation_count].ptr = ptr;
            allocations[allocation_count].size = size;
            allocations[allocation_count].alloc_cpu = cpu;
            allocations[allocation_count].pattern = pattern;
            
            printf("  [%d] CPU %d 分配 %zu 字节，地址: %p\n", 
                   allocation_count, cpu, size, ptr);
            
            allocation_count++;
        }
    }
    
    printf("\n开始循环释放...\n");
    int total_cross_core = 0;
    
    // 循环在不同CPU上释放（错位释放）
    for (int i = 0; i < allocation_count; i++) {
        int free_cpu = (allocations[i].alloc_cpu + 2) % MAX_CORES;  // 错位2个CPU
        set_cpu_id(free_cpu);
        
        if (allocations[i].ptr) {
            // 验证内存
            unsigned char *data = (unsigned char *)allocations[i].ptr;
            int valid = 1;
            for (size_t j = 0; j < allocations[i].size; j++) {
                if (data[j] != allocations[i].pattern) {
                    valid = 0;
                    break;
                }
            }
            
            if (valid) {
                kfree(allocations[i].ptr);
                
                if (allocations[i].alloc_cpu != free_cpu) {
                    total_cross_core++;
                    printf("  [%d] 🔄 跨核释放：分配CPU %d → 释放CPU %d，大小: %zu\n",
                           i, allocations[i].alloc_cpu, free_cpu, allocations[i].size);
                } else {
                    printf("  [%d] ✓ 同核释放：CPU %d，大小: %zu\n",
                           i, free_cpu, allocations[i].size);
                }
            } else {
                printf("  [%d] ❌ 内存验证失败！\n", i);
            }
        }
    }
    
    printf("\n--- 循环测试结果 ---\n");
    printf("总分配数: %d\n", allocation_count);
    printf("跨核释放次数: %d\n", total_cross_core);
    printf("跨核释放比例: %.1f%%\n", 
           (double)total_cross_core / allocation_count * 100.0);
    
    if (total_cross_core > 0) {
        printf("🎉 循环跨核测试成功！\n");
    }
}

/**
 * 随机跨核测试
 */
static void random_cross_core_test(void) {
    printf("\n=== 随机跨核测试 ===\n");
    
    allocation_count = 0;
    memset(allocations, 0, sizeof(allocations));
    
    srand((unsigned int)time(NULL));
    
    // 随机在不同CPU上分配
    for (int i = 0; i < MAX_ALLOCATIONS; i++) {
        int cpu = rand() % MAX_CORES;
        set_cpu_id(cpu);
        
        size_t size = 64 + (rand() % 16) * 64;  // 64-1024字节随机
        void *ptr = kmalloc(size);
        
        if (ptr) {
            unsigned char pattern = (unsigned char)(rand() & 0xFF);
            memset(ptr, pattern, size);
            
            allocations[allocation_count].ptr = ptr;
            allocations[allocation_count].size = size;
            allocations[allocation_count].alloc_cpu = cpu;
            allocations[allocation_count].pattern = pattern;
            
            allocation_count++;
        }
    }
    
    printf("随机分配完成，总数: %d\n", allocation_count);
    
    // 随机在不同CPU上释放
    int total_cross_core = 0;
    for (int i = 0; i < allocation_count; i++) {
        int free_cpu = rand() % MAX_CORES;
        set_cpu_id(free_cpu);
        
        if (allocations[i].ptr) {
            // 验证并释放
            unsigned char *data = (unsigned char *)allocations[i].ptr;
            int valid = 1;
            for (size_t j = 0; j < allocations[i].size; j++) {
                if (data[j] != allocations[i].pattern) {
                    valid = 0;
                    break;
                }
            }
            
            if (valid) {
                kfree(allocations[i].ptr);
                
                if (allocations[i].alloc_cpu != free_cpu) {
                    total_cross_core++;
                }
            }
        }
    }
    
    printf("\n--- 随机测试结果 ---\n");
    printf("跨核释放次数: %d/%d\n", total_cross_core, allocation_count);
    printf("跨核释放比例: %.1f%%\n", 
           (double)total_cross_core / allocation_count * 100.0);
    
    if (total_cross_core > 0) {
        printf("🎉 随机跨核测试成功！\n");
    }
}

/**
 * 主函数
 */
int main(void) {
    printf("SLUB分配器强制跨核内存分配释放测试程序\n");
    printf("=========================================\n\n");
    
    // 初始化SLUB分配器
    printf("初始化SLUB分配器...\n");
    if (slub_init() != 0) {
        printf("SLUB分配器初始化失败\n");
        return 1;
    }
    printf("✓ SLUB分配器初始化成功\n");
    
    // 运行各种跨核测试
    cross_allocation_test();
    round_robin_test();
    random_cross_core_test();
    
    // 打印最终统计
    printf("\n");
    slub_print_stats();
    
    // 清理SLUB分配器
    printf("\n清理SLUB分配器...\n");
    slub_cleanup();
    
    printf("\n强制跨核测试完成！\n");
    return 0;
}
