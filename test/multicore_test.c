#include "slub.h"
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <pthread.h>
#include <time.h>
#include <unistd.h>
#include <assert.h>
#include <sched.h>

/* macOS不支持pthread_barrier，实现一个简单的替代方案 */
typedef struct {
    pthread_mutex_t mutex;
    pthread_cond_t cond;
    int count;
    int waiting;
} pthread_barrier_t;

static int pthread_barrier_init(pthread_barrier_t *barrier, void *attr, int count) {
    (void)attr;  /* 未使用 */
    barrier->count = count;
    barrier->waiting = 0;
    pthread_mutex_init(&barrier->mutex, NULL);
    pthread_cond_init(&barrier->cond, NULL);
    return 0;
}

static int pthread_barrier_wait(pthread_barrier_t *barrier) {
    pthread_mutex_lock(&barrier->mutex);
    barrier->waiting++;
    if (barrier->waiting >= barrier->count) {
        barrier->waiting = 0;
        pthread_cond_broadcast(&barrier->cond);
        pthread_mutex_unlock(&barrier->mutex);
        return 1;  /* 最后一个线程 */
    } else {
        pthread_cond_wait(&barrier->cond, &barrier->mutex);
        pthread_mutex_unlock(&barrier->mutex);
        return 0;
    }
}

static int pthread_barrier_destroy(pthread_barrier_t *barrier) {
    pthread_mutex_destroy(&barrier->mutex);
    pthread_cond_destroy(&barrier->cond);
    return 0;
}

/* 测试配置 */
#define MAX_TEST_THREADS 16
#define TEST_ITERATIONS 5000
#define TEST_OBJECTS_PER_ITERATION 50

/* 测试结果统计 */
struct thread_stats {
    int thread_id;
    int cpu_id;
    unsigned long alloc_count;
    unsigned long free_count;
    unsigned long alloc_time_ns;
    unsigned long free_time_ns;
    unsigned long errors;
    unsigned long cpu_migrations;
};

/* 线程测试参数 */
struct thread_param {
    int thread_id;
    int target_cpu;
    int iterations;
    int objects_per_iteration;
    struct thread_stats stats;
    pthread_barrier_t *start_barrier;
};

/**
 * 获取纳秒时间戳
 */
static unsigned long get_time_ns(void) {
    struct timespec ts;
    clock_gettime(CLOCK_MONOTONIC, &ts);
    return ts.tv_sec * 1000000000UL + ts.tv_nsec;
}

/**
 * 多核压力测试线程函数
 */
static void *multicore_test_thread(void *arg) {
    struct thread_param *param = (struct thread_param *)arg;
    void **objects = malloc(param->objects_per_iteration * sizeof(void *));
    
    if (!objects) {
        param->stats.errors++;
        return NULL;
    }
    
    /* 初始化统计信息 */
    param->stats.thread_id = param->thread_id;
    param->stats.alloc_count = 0;
    param->stats.free_count = 0;
    param->stats.alloc_time_ns = 0;
    param->stats.free_time_ns = 0;
    param->stats.errors = 0;
    param->stats.cpu_migrations = 0;
    
    /* 尝试绑定到指定CPU */
    if (param->target_cpu >= 0) {
        set_thread_affinity(param->target_cpu);
    }
    
    /* 等待所有线程准备就绪 */
    pthread_barrier_wait(param->start_barrier);
    
    printf("[线程 %d] 开始测试，目标CPU: %d，迭代次数: %d\n", 
           param->thread_id, param->target_cpu, param->iterations);
    
    int last_cpu = get_cpu_id();
    param->stats.cpu_id = last_cpu;
    
    for (int iter = 0; iter < param->iterations; iter++) {
        /* 检查CPU迁移 */
        int current_cpu = get_cpu_id();
        if (current_cpu != last_cpu) {
            param->stats.cpu_migrations++;
            last_cpu = current_cpu;
        }
        
        /* 分配阶段 */
        unsigned long start_time = get_time_ns();
        
        for (int i = 0; i < param->objects_per_iteration; i++) {
            /* 使用不同大小的对象测试不同的缓存 */
            size_t sizes[] = {32, 64, 128, 256, 512, 1024};
            size_t size = sizes[i % 6];
            
            objects[i] = kmalloc(size);
            
            if (!objects[i]) {
                param->stats.errors++;
                continue;
            }
            
            param->stats.alloc_count++;
            
            /* 写入测试数据 */
            memset(objects[i], 0x55 + (i % 16), size);
        }
        
        unsigned long alloc_end = get_time_ns();
        param->stats.alloc_time_ns += (alloc_end - start_time);
        
        /* 验证数据完整性 */
        for (int i = 0; i < param->objects_per_iteration; i++) {
            if (objects[i]) {
                unsigned char *data = (unsigned char *)objects[i];
                unsigned char expected = 0x55 + (i % 16);
                if (data[0] != expected) {
                    param->stats.errors++;
                }
            }
        }
        
        /* 释放阶段 */
        unsigned long free_start = get_time_ns();
        
        for (int i = 0; i < param->objects_per_iteration; i++) {
            if (objects[i]) {
                kfree(objects[i]);
                param->stats.free_count++;
            }
        }
        
        unsigned long free_end = get_time_ns();
        param->stats.free_time_ns += (free_end - free_start);
        
        /* 每1000次迭代打印进度 */
        if ((iter + 1) % 1000 == 0) {
            printf("[线程 %d] 完成 %d/%d 迭代，当前CPU: %d\n", 
                   param->thread_id, iter + 1, param->iterations, current_cpu);
        }
    }
    
    free(objects);
    printf("[线程 %d] 测试完成，CPU迁移次数: %lu\n", 
           param->thread_id, param->stats.cpu_migrations);
    return NULL;
}

/**
 * 运行多核压力测试
 */
static bool run_multicore_stress_test(int num_threads) {
    printf("\n=== 多核压力测试 ===\n");
    printf("线程数: %d, 每线程迭代: %d, 每次分配对象: %d\n",
           num_threads, TEST_ITERATIONS, TEST_OBJECTS_PER_ITERATION);
    
    pthread_t threads[MAX_TEST_THREADS];
    struct thread_param params[MAX_TEST_THREADS];
    pthread_barrier_t start_barrier;
    
    /* 初始化同步屏障 */
    if (pthread_barrier_init(&start_barrier, NULL, num_threads) != 0) {
        printf("错误：无法初始化同步屏障\n");
        return false;
    }
    
    int system_cpus = get_system_cpu_count();
    printf("系统CPU数量: %d\n", system_cpus);
    
    /* 初始化线程参数 */
    for (int i = 0; i < num_threads; i++) {
        params[i].thread_id = i;
        params[i].target_cpu = i % system_cpus;  /* 轮询分配到不同CPU */
        params[i].iterations = TEST_ITERATIONS;
        params[i].objects_per_iteration = TEST_OBJECTS_PER_ITERATION;
        params[i].start_barrier = &start_barrier;
    }
    
    /* 启动线程 */
    unsigned long test_start = get_time_ns();
    
    for (int i = 0; i < num_threads; i++) {
        if (pthread_create(&threads[i], NULL, multicore_test_thread, &params[i]) != 0) {
            printf("错误：无法创建线程 %d\n", i);
            pthread_barrier_destroy(&start_barrier);
            return false;
        }
    }
    
    /* 等待线程完成 */
    for (int i = 0; i < num_threads; i++) {
        pthread_join(threads[i], NULL);
    }
    
    unsigned long test_end = get_time_ns();
    unsigned long total_time = test_end - test_start;
    
    /* 统计结果 */
    struct thread_stats total_stats = {0};
    unsigned long total_migrations = 0;
    
    printf("\n--- 线程统计 ---\n");
    for (int i = 0; i < num_threads; i++) {
        struct thread_stats *stats = &params[i].stats;
        total_stats.alloc_count += stats->alloc_count;
        total_stats.free_count += stats->free_count;
        total_stats.alloc_time_ns += stats->alloc_time_ns;
        total_stats.free_time_ns += stats->free_time_ns;
        total_stats.errors += stats->errors;
        total_migrations += stats->cpu_migrations;
        
        printf("线程 %2d: CPU %d, 分配 %6lu, 释放 %6lu, 错误 %3lu, 迁移 %3lu\n",
               i, stats->cpu_id, stats->alloc_count, stats->free_count, 
               stats->errors, stats->cpu_migrations);
    }
    
    printf("\n--- 总体结果 ---\n");
    printf("总分配次数: %lu\n", total_stats.alloc_count);
    printf("总释放次数: %lu\n", total_stats.free_count);
    printf("错误次数: %lu\n", total_stats.errors);
    printf("总CPU迁移次数: %lu\n", total_migrations);
    printf("总测试时间: %.2f 秒\n", (double)total_time / 1000000000.0);
    
    if (total_stats.alloc_count > 0) {
        double avg_alloc_ns = (double)total_stats.alloc_time_ns / total_stats.alloc_count;
        printf("平均分配时间: %.0f 纳秒\n", avg_alloc_ns);
        printf("分配吞吐量: %.0f 次/秒\n", 
               (double)total_stats.alloc_count * 1000000000.0 / total_time);
    }
    
    if (total_stats.free_count > 0) {
        double avg_free_ns = (double)total_stats.free_time_ns / total_stats.free_count;
        printf("平均释放时间: %.0f 纳秒\n", avg_free_ns);
        printf("释放吞吐量: %.0f 次/秒\n", 
               (double)total_stats.free_count * 1000000000.0 / total_time);
    }
    
    /* 计算CPU利用率 */
    double cpu_efficiency = (double)(total_stats.alloc_count + total_stats.free_count) / 
                           (num_threads * (TEST_ITERATIONS * TEST_OBJECTS_PER_ITERATION * 2));
    printf("CPU效率: %.1f%%\n", cpu_efficiency * 100.0);
    
    pthread_barrier_destroy(&start_barrier);
    
    return total_stats.errors == 0;
}

/**
 * 主测试函数
 */
int main(int argc, char *argv[]) {
    (void)argc;  /* 未使用的参数 */
    (void)argv;  /* 未使用的参数 */
    
    printf("SLUB多核性能测试程序\n");
    printf("===================\n");
    
    /* 初始化随机数 */
    srand(time(NULL));
    
    /* 初始化SLUB分配器 */
    printf("初始化SLUB分配器...\n");
    if (slub_init() != 0) {
        printf("错误：SLUB分配器初始化失败\n");
        return 1;
    }
    
    bool all_tests_passed = true;
    int system_cpus = get_system_cpu_count();
    
    /* 测试不同线程数 */
    int thread_counts[] = {1, 2, 4, 8, system_cpus, system_cpus * 2};
    int test_count = sizeof(thread_counts) / sizeof(thread_counts[0]);
    
    for (int i = 0; i < test_count; i++) {
        int num_threads = thread_counts[i];
        if (num_threads > MAX_TEST_THREADS) {
            num_threads = MAX_TEST_THREADS;
        }
        
        printf("\n测试 %d 个线程...\n", num_threads);
        bool test_passed = run_multicore_stress_test(num_threads);
        all_tests_passed &= test_passed;
        
        if (test_passed) {
            printf("✓ %d线程测试通过\n", num_threads);
        } else {
            printf("✗ %d线程测试失败\n", num_threads);
        }
        
        /* 打印当前统计信息 */
        slub_print_stats();
        
        /* 短暂休息 */
        sleep(1);
    }
    
    /* 验证完整性 */
    printf("\n=== 完整性验证 ===\n");
    bool integrity_ok = slub_verify_integrity();
    all_tests_passed &= integrity_ok;
    
    /* 清理 */
    printf("\n清理SLUB分配器...\n");
    slub_cleanup();
    
    /* 测试结果 */
    printf("\n");
    printf("===================\n");
    if (all_tests_passed) {
        printf("所有多核测试通过！✓\n");
        return 0;
    } else {
        printf("部分多核测试失败！✗\n");
        return 1;
    }
}
