/**
 * 简化的SLUB分配器跨核内存分配释放测试程序
 * 
 * 避免复杂的线程同步，使用更简单的方法测试跨核释放
 */

#include "slub.h"
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <pthread.h>
#include <time.h>
#include <unistd.h>

/* 测试配置 */
#define MAX_ALLOCATIONS 100
#define MAX_THREADS 4

/* 分配记录 */
typedef struct {
    void *ptr;
    size_t size;
    int alloc_cpu;
    unsigned char pattern;
} allocation_record_t;

/* 共享数据 */
static allocation_record_t allocations[MAX_ALLOCATIONS];
static volatile int allocation_count = 0;
static volatile int free_count = 0;
static volatile int cross_core_count = 0;
static pthread_mutex_t shared_mutex = PTHREAD_MUTEX_INITIALIZER;

/* 线程数据 */
typedef struct {
    int thread_id;
    int is_allocator;  // 1=分配器, 0=释放器
} thread_data_t;

/**
 * 分配器线程
 */
static void *allocator_thread(void *arg) {
    thread_data_t *data = (thread_data_t *)arg;
    int cpu_id = get_cpu_id();
    
    printf("[分配线程 %d] 开始运行，CPU: %d\n", data->thread_id, cpu_id);
    
    // 分配一些内存
    for (int i = 0; i < MAX_ALLOCATIONS / 2; i++) {
        size_t size = 64 + (i % 8) * 64;  // 64, 128, 192, ..., 512字节
        void *ptr = kmalloc(size);
        
        if (ptr) {
            unsigned char pattern = (unsigned char)(data->thread_id * 16 + i);
            memset(ptr, pattern, size);
            
            // 记录分配信息
            pthread_mutex_lock(&shared_mutex);
            if (allocation_count < MAX_ALLOCATIONS) {
                allocations[allocation_count].ptr = ptr;
                allocations[allocation_count].size = size;
                allocations[allocation_count].alloc_cpu = cpu_id;
                allocations[allocation_count].pattern = pattern;
                allocation_count++;
                
                printf("[分配线程 %d] 分配 %zu 字节，CPU %d，地址: %p，模式: 0x%02X，总数: %d\n",
                       data->thread_id, size, cpu_id, ptr, pattern, allocation_count);
            }
            pthread_mutex_unlock(&shared_mutex);
            
            // 短暂休眠
            usleep(10000);
        } else {
            printf("[分配线程 %d] 分配 %zu 字节失败\n", data->thread_id, size);
        }
    }
    
    printf("[分配线程 %d] 分配完成\n", data->thread_id);
    return NULL;
}

/**
 * 释放器线程
 */
static void *deallocator_thread(void *arg) {
    thread_data_t *data = (thread_data_t *)arg;
    int cpu_id = get_cpu_id();
    
    printf("[释放线程 %d] 开始运行，CPU: %d\n", data->thread_id, cpu_id);
    
    // 等待一些分配完成
    sleep(1);
    
    // 释放内存
    while (free_count < MAX_ALLOCATIONS / 2) {
        allocation_record_t record = {0};
        int found = 0;
        
        // 查找可释放的内存
        pthread_mutex_lock(&shared_mutex);
        for (int i = 0; i < allocation_count; i++) {
            if (allocations[i].ptr != NULL) {
                record = allocations[i];
                allocations[i].ptr = NULL;  // 标记为已取出
                found = 1;
                break;
            }
        }
        pthread_mutex_unlock(&shared_mutex);
        
        if (found) {
            // 验证内存内容
            unsigned char *data_ptr = (unsigned char *)record.ptr;
            int valid = 1;
            for (size_t j = 0; j < record.size; j++) {
                if (data_ptr[j] != record.pattern) {
                    valid = 0;
                    break;
                }
            }
            
            if (valid) {
                // 释放内存
                kfree(record.ptr);
                
                pthread_mutex_lock(&shared_mutex);
                free_count++;
                if (record.alloc_cpu != cpu_id) {
                    cross_core_count++;
                    printf("[释放线程 %d] 🔄 跨核释放：分配CPU %d → 释放CPU %d，大小: %zu，地址: %p\n",
                           data->thread_id, record.alloc_cpu, cpu_id, record.size, record.ptr);
                } else {
                    printf("[释放线程 %d] ✓ 同核释放：CPU %d，大小: %zu，地址: %p\n",
                           data->thread_id, cpu_id, record.size, record.ptr);
                }
                pthread_mutex_unlock(&shared_mutex);
            } else {
                printf("[释放线程 %d] ❌ 内存验证失败！地址: %p，大小: %zu\n",
                       data->thread_id, record.ptr, record.size);
                pthread_mutex_lock(&shared_mutex);
                free_count++;
                pthread_mutex_unlock(&shared_mutex);
            }
        } else {
            // 没有可释放的内存，短暂休眠
            usleep(50000);
        }
    }
    
    printf("[释放线程 %d] 释放完成\n", data->thread_id);
    return NULL;
}

/**
 * 简单的跨核测试
 */
static void simple_cross_core_test(void) {
    printf("\n=== 简单跨核内存分配释放测试 ===\n");
    
    pthread_t threads[MAX_THREADS];
    thread_data_t thread_data[MAX_THREADS];
    
    // 重置计数器
    allocation_count = 0;
    free_count = 0;
    cross_core_count = 0;
    
    // 清空分配记录
    memset(allocations, 0, sizeof(allocations));
    
    printf("启动 %d 个线程...\n", MAX_THREADS);
    
    // 创建线程：前一半分配，后一半释放
    for (int i = 0; i < MAX_THREADS; i++) {
        thread_data[i].thread_id = i;
        thread_data[i].is_allocator = (i < MAX_THREADS / 2);
        
        void *(*thread_func)(void*) = thread_data[i].is_allocator ? 
                                      allocator_thread : deallocator_thread;
        
        if (pthread_create(&threads[i], NULL, thread_func, &thread_data[i]) != 0) {
            printf("创建线程 %d 失败\n", i);
            return;
        }
        
        // 短暂延迟，让线程获得不同的CPU
        usleep(100000);
    }
    
    // 等待所有线程完成
    for (int i = 0; i < MAX_THREADS; i++) {
        pthread_join(threads[i], NULL);
    }
    
    // 清理剩余的分配
    printf("\n清理剩余分配...\n");
    for (int i = 0; i < allocation_count; i++) {
        if (allocations[i].ptr != NULL) {
            kfree(allocations[i].ptr);
            printf("清理剩余分配: %p\n", allocations[i].ptr);
        }
    }
    
    // 打印结果
    printf("\n--- 测试结果 ---\n");
    printf("总分配次数: %d\n", allocation_count);
    printf("总释放次数: %d\n", free_count);
    printf("跨核释放次数: %d\n", cross_core_count);
    printf("同核释放次数: %d\n", free_count - cross_core_count);
    
    if (cross_core_count > 0) {
        printf("跨核释放比例: %.1f%%\n", 
               (double)cross_core_count / free_count * 100.0);
        printf("🎉 跨核测试成功！SLUB分配器支持跨核内存释放\n");
    } else {
        printf("⚠️  没有发生跨核释放，可能所有线程都在同一个CPU上\n");
    }
}

/**
 * 强制跨核测试
 */
static void forced_cross_core_test(void) {
    printf("\n=== 强制跨核测试 ===\n");
    
    // 在当前线程分配内存
    int alloc_cpu = get_cpu_id();
    printf("在CPU %d上分配内存...\n", alloc_cpu);
    
    void *ptrs[10];
    size_t sizes[10];
    unsigned char patterns[10];
    
    for (int i = 0; i < 10; i++) {
        sizes[i] = 128 + i * 64;
        ptrs[i] = kmalloc(sizes[i]);
        patterns[i] = (unsigned char)(0xAA + i);
        
        if (ptrs[i]) {
            memset(ptrs[i], patterns[i], sizes[i]);
            printf("  分配 %zu 字节，地址: %p，模式: 0x%02X\n", 
                   sizes[i], ptrs[i], patterns[i]);
        }
    }
    
    // 强制刷新CPU ID，模拟线程迁移
    printf("\n模拟线程迁移到不同CPU...\n");
    int free_cpu = get_cpu_id_refresh();
    printf("现在在CPU %d上释放内存...\n", free_cpu);
    
    // 验证并释放内存
    int cross_core_frees = 0;
    for (int i = 0; i < 10; i++) {
        if (ptrs[i]) {
            // 验证内存内容
            unsigned char *data = (unsigned char *)ptrs[i];
            int valid = 1;
            for (size_t j = 0; j < sizes[i]; j++) {
                if (data[j] != patterns[i]) {
                    valid = 0;
                    break;
                }
            }
            
            if (valid) {
                kfree(ptrs[i]);
                if (alloc_cpu != free_cpu) {
                    cross_core_frees++;
                    printf("  🔄 跨核释放：分配CPU %d → 释放CPU %d，大小: %zu\n",
                           alloc_cpu, free_cpu, sizes[i]);
                } else {
                    printf("  ✓ 同核释放：CPU %d，大小: %zu\n", free_cpu, sizes[i]);
                }
            } else {
                printf("  ❌ 内存验证失败！大小: %zu\n", sizes[i]);
            }
        }
    }
    
    printf("\n强制跨核测试结果：\n");
    printf("分配CPU: %d\n", alloc_cpu);
    printf("释放CPU: %d\n", free_cpu);
    printf("跨核释放次数: %d/10\n", cross_core_frees);
    
    if (alloc_cpu != free_cpu && cross_core_frees > 0) {
        printf("🎉 强制跨核测试成功！\n");
    } else {
        printf("ℹ️  CPU ID未发生变化，这在单核或CPU亲和性固定的系统中是正常的\n");
    }
}

/**
 * 主函数
 */
int main(void) {
    printf("SLUB分配器跨核内存分配释放测试程序\n");
    printf("=====================================\n\n");
    
    // 初始化随机数种子
    srand((unsigned int)time(NULL));
    
    // 初始化SLUB分配器
    printf("初始化SLUB分配器...\n");
    if (slub_init() != 0) {
        printf("SLUB分配器初始化失败\n");
        return 1;
    }
    printf("✓ SLUB分配器初始化成功\n");
    
    // 运行简单跨核测试
    simple_cross_core_test();
    
    // 运行强制跨核测试
    forced_cross_core_test();
    
    // 打印最终统计
    printf("\n");
    slub_print_stats();
    
    // 清理SLUB分配器
    printf("\n清理SLUB分配器...\n");
    slub_cleanup();
    
    printf("\n跨核测试完成！\n");
    return 0;
}
