#include "slub.h"
#include <stdio.h>
#include <string.h>

/* 外部函数声明 */
extern int page_allocator_init(struct page_allocator *alloc);
extern void page_allocator_cleanup(struct page_allocator *alloc);

/**
 * 初始化预定义的kmalloc缓存
 */
static int init_kmalloc_caches(void) {
    /* kmalloc缓存大小数组 - SLUB只支持小于页大小的对象 */
    static const size_t sizes[] = {
        8, 16, 32, 64, 128, 256, 512, 1024, 2048
    };
    static const int count = sizeof(sizes) / sizeof(sizes[0]);
    
    printf("[SLUB_INIT] 初始化 %d 个kmalloc缓存\n", count);
    
    for (int i = 0; i < count; i++) {
        char name[32];
        snprintf(name, sizeof(name), "kmalloc-%zu", sizes[i]);
        
        kmalloc_caches[i] = kmem_cache_create(name, sizes[i], 0, 0);
        if (!kmalloc_caches[i]) {
            printf("[SLUB_INIT] 错误：无法创建 %s 缓存\n", name);
            
            /* 清理已创建的缓存 */
            for (int j = 0; j < i; j++) {
                if (kmalloc_caches[j]) {
                    kmem_cache_destroy(kmalloc_caches[j]);
                    kmalloc_caches[j] = NULL;
                }
            }
            return -1;
        }
        
        printf("[SLUB_INIT] 创建缓存 %s 成功\n", name);
    }
    
    return 0;
}

/**
 * 清理预定义的kmalloc缓存
 */
static void cleanup_kmalloc_caches(void) {
    printf("[SLUB_INIT] 清理kmalloc缓存\n");
    
    for (int i = 0; i < 13; i++) {
        if (kmalloc_caches[i]) {
            kmem_cache_destroy(kmalloc_caches[i]);
            kmalloc_caches[i] = NULL;
        }
    }
}

/**
 * 初始化SLUB分配器
 * 
 * 初始化顺序：
 * 1. 初始化全局状态
 * 2. 初始化页分配器
 * 3. 创建预定义的kmalloc缓存
 */
int slub_init(void) {
    printf("\n=== SLUB分配器初始化开始 ===\n");
    
    /* 检查是否已经初始化 */
    if (g_slub.initialized) {
        printf("[SLUB_INIT] 警告：SLUB分配器已经初始化\n");
        return 0;
    }
    
    /* 初始化全局状态 */
    memset(&g_slub, 0, sizeof(g_slub));
    
    /* 初始化缓存链表自旋锁 */
    spin_lock_init(&g_slub.cache_lock);

    /* 初始化页分配器 */
    printf("[SLUB_INIT] 初始化页分配器\n");
    if (page_allocator_init(&g_slub.page_alloc) != 0) {
        printf("[SLUB_INIT] 错误：页分配器初始化失败\n");
        return -1;
    }

    /* 创建预定义的kmalloc缓存 */
    if (init_kmalloc_caches() != 0) {
        printf("[SLUB_INIT] 错误：kmalloc缓存初始化失败\n");
        page_allocator_cleanup(&g_slub.page_alloc);
        return -1;
    }
    
    /* 标记为已初始化 */
    g_slub.initialized = true;
    
    printf("[SLUB_INIT] SLUB分配器初始化成功\n");
    printf("=== SLUB分配器初始化完成 ===\n\n");
    
    return 0;
}

/**
 * 清理SLUB分配器
 * 
 * 清理顺序：
 * 1. 清理所有用户创建的缓存
 * 2. 清理预定义的kmalloc缓存
 * 3. 清理页分配器
 * 4. 清理全局状态
 */
void slub_cleanup(void) {
    printf("\n=== SLUB分配器清理开始 ===\n");
    
    if (!g_slub.initialized) {
        printf("[SLUB_INIT] 警告：SLUB分配器未初始化\n");
        return;
    }
    
    /* 清理所有用户创建的缓存 */
    printf("[SLUB_INIT] 清理用户缓存\n");
    spin_lock(&g_slub.cache_lock);
    struct kmem_cache *cache = g_slub.cache_list;
    while (cache) {
        struct kmem_cache *next = cache->next;

        /* 跳过kmalloc缓存，它们会单独清理 */
        bool is_kmalloc_cache = false;
        for (int i = 0; i < 13; i++) {
            if (cache == kmalloc_caches[i]) {
                is_kmalloc_cache = true;
                break;
            }
        }

        if (!is_kmalloc_cache) {
            printf("[SLUB_INIT] 清理用户缓存: %s\n", cache->name);
            /* 从链表中移除 */
            struct kmem_cache **curr = &g_slub.cache_list;
            while (*curr && *curr != cache) {
                curr = &(*curr)->next;
            }
            if (*curr) {
                *curr = cache->next;
            }

            spin_unlock(&g_slub.cache_lock);
            kmem_cache_destroy(cache);
            spin_lock(&g_slub.cache_lock);

            /* 重新开始遍历，因为链表已经改变 */
            cache = g_slub.cache_list;
            continue;
        }

        cache = next;
    }
    spin_unlock(&g_slub.cache_lock);
    
    /* 清理预定义的kmalloc缓存 */
    cleanup_kmalloc_caches();
    
    /* 清理页分配器 */
    printf("[SLUB_INIT] 清理页分配器\n");
    page_allocator_cleanup(&g_slub.page_alloc);
    
    /* 清理全局状态 */
    /* 自旋锁不需要显式销毁 */
    g_slub.cache_list = NULL;
    g_slub.initialized = false;
    
    printf("[SLUB_INIT] SLUB分配器清理完成\n");
    printf("=== SLUB分配器清理结束 ===\n\n");
}

/**
 * 检查SLUB分配器是否已初始化
 */
bool slub_is_initialized(void) {
    return g_slub.initialized;
}

/**
 * 获取SLUB分配器状态信息
 */
void slub_get_info(int *total_caches, int *total_pages, size_t *total_memory) {
    if (!g_slub.initialized) {
        if (total_caches) *total_caches = 0;
        if (total_pages) *total_pages = 0;
        if (total_memory) *total_memory = 0;
        return;
    }
    
    /* 统计缓存数量 */
    int cache_count = 0;
    spin_lock(&g_slub.cache_lock);
    struct kmem_cache *cache = g_slub.cache_list;
    while (cache) {
        cache_count++;
        cache = cache->next;
    }
    spin_unlock(&g_slub.cache_lock);
    
    if (total_caches) *total_caches = cache_count;
    if (total_pages) {
        *total_pages = g_slub.page_alloc.total_pages - g_slub.page_alloc.free_pages;
    }
    if (total_memory) {
        *total_memory = (g_slub.page_alloc.total_pages - g_slub.page_alloc.free_pages) * PAGE_SIZE;
    }
}

/**
 * 强制垃圾回收（释放空闲页）
 */
void slub_gc(void) {
    if (!g_slub.initialized) return;
    
    printf("[SLUB_INIT] 执行垃圾回收\n");
    
    /* 遍历所有缓存，释放空闲页 */
    spin_lock(&g_slub.cache_lock);
    struct kmem_cache *cache = g_slub.cache_list;
    while (cache) {
        /* 这里可以实现更复杂的垃圾回收逻辑 */
        /* 例如：释放长时间未使用的partial页 */
        cache = cache->next;
    }
    spin_unlock(&g_slub.cache_lock);
    
    printf("[SLUB_INIT] 垃圾回收完成\n");
}
