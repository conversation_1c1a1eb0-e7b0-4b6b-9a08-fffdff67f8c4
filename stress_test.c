/**
 * SLUB分配器压力测试程序
 * 
 * 测试各种极端情况和边界条件：
 * 1. 大量小对象分配
 * 2. 大对象分配
 * 3. 混合大小分配
 * 4. 内存碎片化测试
 * 5. 长时间运行测试
 * 6. 内存泄漏检测
 * 7. 并发压力测试
 * 8. 边界条件测试
 */

#include "slub.h"
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <pthread.h>
#include <time.h>
#include <unistd.h>
#include <assert.h>
#include <signal.h>
#include <sys/time.h>

/* macOS不支持pthread_barrier，实现一个简单的替代方案 */
typedef struct {
    pthread_mutex_t mutex;
    pthread_cond_t cond;
    int count;
    int waiting;
} pthread_barrier_t;

static int pthread_barrier_init(pthread_barrier_t *barrier, void *attr, int count) {
    (void)attr;
    barrier->count = count;
    barrier->waiting = 0;
    pthread_mutex_init(&barrier->mutex, NULL);
    pthread_cond_init(&barrier->cond, NULL);
    return 0;
}

static int pthread_barrier_wait(pthread_barrier_t *barrier) {
    pthread_mutex_lock(&barrier->mutex);
    barrier->waiting++;
    if (barrier->waiting >= barrier->count) {
        barrier->waiting = 0;
        pthread_cond_broadcast(&barrier->cond);
        pthread_mutex_unlock(&barrier->mutex);
        return 1;
    } else {
        pthread_cond_wait(&barrier->cond, &barrier->mutex);
        pthread_mutex_unlock(&barrier->mutex);
        return 0;
    }
}

static int pthread_barrier_destroy(pthread_barrier_t *barrier) {
    pthread_mutex_destroy(&barrier->mutex);
    pthread_cond_destroy(&barrier->cond);
    return 0;
}

/* 测试配置 */
#define MAX_THREADS 32
#define MAX_ALLOCATIONS 100000
#define MAX_OBJECT_SIZE 2048
#define MIN_OBJECT_SIZE 8

/* 全局变量 */
static volatile int test_running = 1;
static volatile int total_allocations = 0;
static volatile int total_frees = 0;
static volatile int allocation_errors = 0;
static volatile int free_errors = 0;
static pthread_mutex_t stats_mutex = PTHREAD_MUTEX_INITIALIZER;

/* 测试统计 */
typedef struct {
    int thread_id;
    int allocations;
    int frees;
    int errors;
    double total_time;
    size_t max_memory_used;
    size_t current_memory_used;
} thread_stats_t;

static thread_stats_t thread_stats[MAX_THREADS];

/* 分配记录 */
typedef struct allocation {
    void *ptr;
    size_t size;
    struct allocation *next;
} allocation_t;

/* 线程数据 */
typedef struct {
    int thread_id;
    int iterations;
    int max_concurrent_allocs;
    pthread_barrier_t *barrier;
    allocation_t *allocations;
} thread_data_t;

/**
 * 获取当前时间（毫秒）
 */
static double get_time_ms(void) {
    struct timeval tv;
    gettimeofday(&tv, NULL);
    return tv.tv_sec * 1000.0 + tv.tv_usec / 1000.0;
}

/**
 * 信号处理函数
 */
static void signal_handler(int sig) {
    (void)sig;
    printf("\n收到中断信号，停止测试...\n");
    test_running = 0;
}

/**
 * 更新统计信息
 */
static void update_stats(int thread_id, int alloc_delta, int free_delta, int error_delta) {
    pthread_mutex_lock(&stats_mutex);
    total_allocations += alloc_delta;
    total_frees += free_delta;
    allocation_errors += error_delta;
    thread_stats[thread_id].allocations += alloc_delta;
    thread_stats[thread_id].frees += free_delta;
    thread_stats[thread_id].errors += error_delta;
    pthread_mutex_unlock(&stats_mutex);
}

/**
 * 随机大小生成器
 */
static size_t get_random_size(void) {
    static const size_t sizes[] = {8, 16, 32, 64, 128, 256, 512, 1024, 2048};
    // static const int num_sizes = sizeof(sizes) / sizeof(sizes[0]);

    int choice = rand() % 100;

    if (choice < 40) {
        // 40% 小对象 (8-64字节)
        return sizes[rand() % 4];
    } else if (choice < 70) {
        // 30% 中等对象 (128-512字节)
        return sizes[4 + rand() % 3];
    } else {
        // 30% 大对象 (1024-2048字节)
        return sizes[7 + rand() % 2];
    }
}

/**
 * 验证内存内容
 */
static int verify_memory(void *ptr, size_t size, unsigned char pattern) {
    unsigned char *data = (unsigned char *)ptr;
    for (size_t i = 0; i < size; i++) {
        if (data[i] != pattern) {
            return 0;
        }
    }
    return 1;
}

/**
 * 基础分配测试线程
 */
static void *basic_alloc_thread(void *arg) {
    thread_data_t *data = (thread_data_t *)arg;
    double start_time = get_time_ms();
    
    printf("[线程 %d] 开始基础分配测试，迭代次数: %d\n", 
           data->thread_id, data->iterations);
    
    // 等待所有线程准备就绪
    pthread_barrier_wait(data->barrier);
    
    for (int i = 0; i < data->iterations && test_running; i++) {
        size_t size = get_random_size();
        void *ptr = kmalloc(size);
        
        if (ptr) {
            // 写入测试模式
            unsigned char pattern = (unsigned char)(data->thread_id + i);
            memset(ptr, pattern, size);
            
            // 立即验证
            if (!verify_memory(ptr, size, pattern)) {
                printf("[线程 %d] 内存验证失败！\n", data->thread_id);
                update_stats(data->thread_id, 0, 0, 1);
            }
            
            // 立即释放
            kfree(ptr);
            update_stats(data->thread_id, 1, 1, 0);
        } else {
            update_stats(data->thread_id, 0, 0, 1);
        }
        
        // 每1000次迭代报告进度
        if ((i + 1) % 1000 == 0) {
            printf("[线程 %d] 完成 %d/%d 迭代\n", 
                   data->thread_id, i + 1, data->iterations);
        }
    }
    
    double end_time = get_time_ms();
    thread_stats[data->thread_id].total_time = end_time - start_time;
    
    printf("[线程 %d] 基础测试完成，耗时: %.2f ms\n", 
           data->thread_id, thread_stats[data->thread_id].total_time);
    
    return NULL;
}

/**
 * 内存保持测试线程（分配后保持一段时间再释放）
 */
static void *memory_hold_thread(void *arg) {
    thread_data_t *data = (thread_data_t *)arg;
    allocation_t *allocations = NULL;
    int alloc_count = 0;
    double start_time = get_time_ms();
    
    printf("[线程 %d] 开始内存保持测试，最大并发分配: %d\n", 
           data->thread_id, data->max_concurrent_allocs);
    
    pthread_barrier_wait(data->barrier);
    
    for (int i = 0; i < data->iterations && test_running; i++) {
        // 分配阶段
        if (alloc_count < data->max_concurrent_allocs) {
            size_t size = get_random_size();
            void *ptr = kmalloc(size);
            
            if (ptr) {
                allocation_t *alloc = malloc(sizeof(allocation_t));
                if (alloc) {
                    alloc->ptr = ptr;
                    alloc->size = size;
                    alloc->next = allocations;
                    allocations = alloc;
                    alloc_count++;
                    
                    // 写入测试模式
                    unsigned char pattern = (unsigned char)(data->thread_id + i);
                    memset(ptr, pattern, size);
                    
                    update_stats(data->thread_id, 1, 0, 0);
                    thread_stats[data->thread_id].current_memory_used += size;
                    if (thread_stats[data->thread_id].current_memory_used > 
                        thread_stats[data->thread_id].max_memory_used) {
                        thread_stats[data->thread_id].max_memory_used = 
                            thread_stats[data->thread_id].current_memory_used;
                    }
                } else {
                    kfree(ptr);
                    update_stats(data->thread_id, 0, 0, 1);
                }
            } else {
                update_stats(data->thread_id, 0, 0, 1);
            }
        }
        
        // 随机释放一些内存
        if (allocations && (rand() % 3 == 0)) {
            allocation_t *to_free = allocations;
            allocations = allocations->next;
            
            // 验证内存内容
            unsigned char expected_pattern = (unsigned char)(data->thread_id + 
                (i - alloc_count + 1));
            if (!verify_memory(to_free->ptr, to_free->size, expected_pattern)) {
                printf("[线程 %d] 内存损坏检测！\n", data->thread_id);
                update_stats(data->thread_id, 0, 0, 1);
            }
            
            kfree(to_free->ptr);
            thread_stats[data->thread_id].current_memory_used -= to_free->size;
            update_stats(data->thread_id, 0, 1, 0);
            free(to_free);
            alloc_count--;
        }
        
        if ((i + 1) % 1000 == 0) {
            printf("[线程 %d] 完成 %d/%d 迭代，当前分配: %d\n", 
                   data->thread_id, i + 1, data->iterations, alloc_count);
        }
    }
    
    // 释放剩余的所有内存
    while (allocations) {
        allocation_t *to_free = allocations;
        allocations = allocations->next;
        kfree(to_free->ptr);
        thread_stats[data->thread_id].current_memory_used -= to_free->size;
        update_stats(data->thread_id, 0, 1, 0);
        free(to_free);
    }
    
    double end_time = get_time_ms();
    thread_stats[data->thread_id].total_time = end_time - start_time;
    
    printf("[线程 %d] 内存保持测试完成，耗时: %.2f ms\n", 
           data->thread_id, thread_stats[data->thread_id].total_time);
    
    return NULL;
}

/**
 * 碎片化测试线程
 */
static void *fragmentation_thread(void *arg) {
    thread_data_t *data = (thread_data_t *)arg;
    void **ptrs = malloc(sizeof(void*) * data->max_concurrent_allocs);
    size_t *sizes = malloc(sizeof(size_t) * data->max_concurrent_allocs);
    double start_time = get_time_ms();

    printf("[线程 %d] 开始碎片化测试\n", data->thread_id);

    pthread_barrier_wait(data->barrier);

    // 第一阶段：分配大量不同大小的对象
    for (int i = 0; i < data->max_concurrent_allocs && test_running; i++) {
        sizes[i] = get_random_size();
        ptrs[i] = kmalloc(sizes[i]);

        if (ptrs[i]) {
            memset(ptrs[i], (unsigned char)(data->thread_id + i), sizes[i]);
            update_stats(data->thread_id, 1, 0, 0);
        } else {
            update_stats(data->thread_id, 0, 0, 1);
            ptrs[i] = NULL;
        }
    }

    // 第二阶段：随机释放一半的对象，创建碎片
    for (int i = 0; i < data->max_concurrent_allocs / 2 && test_running; i++) {
        int idx = rand() % data->max_concurrent_allocs;
        if (ptrs[idx]) {
            kfree(ptrs[idx]);
            update_stats(data->thread_id, 0, 1, 0);
            ptrs[idx] = NULL;
        }
    }

    // 第三阶段：尝试重新分配，测试碎片整理
    for (int i = 0; i < data->iterations && test_running; i++) {
        int idx = rand() % data->max_concurrent_allocs;

        if (ptrs[idx] == NULL) {
            // 重新分配
            sizes[idx] = get_random_size();
            ptrs[idx] = kmalloc(sizes[idx]);

            if (ptrs[idx]) {
                memset(ptrs[idx], (unsigned char)(data->thread_id + i), sizes[idx]);
                update_stats(data->thread_id, 1, 0, 0);
            } else {
                update_stats(data->thread_id, 0, 0, 1);
            }
        } else {
            // 释放现有对象
            kfree(ptrs[idx]);
            update_stats(data->thread_id, 0, 1, 0);
            ptrs[idx] = NULL;
        }

        if ((i + 1) % 1000 == 0) {
            printf("[线程 %d] 碎片化测试进度: %d/%d\n",
                   data->thread_id, i + 1, data->iterations);
        }
    }

    // 清理剩余内存
    for (int i = 0; i < data->max_concurrent_allocs; i++) {
        if (ptrs[i]) {
            kfree(ptrs[i]);
            update_stats(data->thread_id, 0, 1, 0);
        }
    }

    free(ptrs);
    free(sizes);

    double end_time = get_time_ms();
    thread_stats[data->thread_id].total_time = end_time - start_time;

    printf("[线程 %d] 碎片化测试完成，耗时: %.2f ms\n",
           data->thread_id, thread_stats[data->thread_id].total_time);

    return NULL;
}

/**
 * 边界条件测试
 */
static void boundary_test(void) {
    printf("\n=== 边界条件测试 ===\n");

    // 测试最小分配
    printf("测试最小分配 (1字节)...\n");
    void *ptr1 = kmalloc(1);
    if (ptr1) {
        *(unsigned char*)ptr1 = 0xAA;
        if (*(unsigned char*)ptr1 == 0xAA) {
            printf("✓ 最小分配测试通过\n");
        } else {
            printf("✗ 最小分配数据损坏\n");
        }
        kfree(ptr1);
    } else {
        printf("✗ 最小分配失败\n");
    }

    // 测试零分配
    printf("测试零分配...\n");
    void *ptr0 = kmalloc(0);
    if (ptr0) {
        printf("✓ 零分配返回有效指针\n");
        kfree(ptr0);
    } else {
        printf("✓ 零分配返回NULL（符合预期）\n");
    }

    // 测试最大分配
    printf("测试最大分配 (2048字节)...\n");
    void *ptr_max = kmalloc(2048);
    if (ptr_max) {
        memset(ptr_max, 0xBB, 2048);
        int valid = 1;
        for (int i = 0; i < 2048; i++) {
            if (((char*)ptr_max)[i] != (char)0xBB) {
                valid = 0;
                break;
            }
        }
        if (valid) {
            printf("✓ 最大分配测试通过\n");
        } else {
            printf("✗ 最大分配数据损坏\n");
        }
        kfree(ptr_max);
    } else {
        printf("✗ 最大分配失败\n");
    }

    // 测试超大分配（应该使用页分配器）
    printf("测试超大分配 (4096字节)...\n");
    void *ptr_huge = kmalloc(4096);
    if (ptr_huge) {
        printf("✓ 超大分配成功（使用页分配器）\n");
        kfree(ptr_huge);
    } else {
        printf("✗ 超大分配失败\n");
    }

    // 测试超超大分配（应该失败）
    printf("测试超超大分配 (8192字节)...\n");
    void *ptr_huge2 = kmalloc(8192);
    if (ptr_huge2) {
        printf("✗ 超超大分配意外成功\n");
        kfree(ptr_huge2);
    } else {
        printf("✓ 超超大分配失败（符合预期）\n");
    }

    // 测试NULL指针释放
    printf("测试NULL指针释放...\n");
    kfree(NULL);
    printf("✓ NULL指针释放安全\n");

    // 测试重复释放检测
    printf("测试重复释放检测...\n");
    void *ptr_dup = kmalloc(64);
    if (ptr_dup) {
        kfree(ptr_dup);
        printf("✓ 第一次释放成功\n");
        // 注意：重复释放可能导致程序崩溃，这里只是演示
        printf("✓ 重复释放检测跳过（避免崩溃）\n");
    }
}

/**
 * 内存泄漏检测测试
 */
static void memory_leak_test(void) {
    printf("\n=== 内存泄漏检测测试 ===\n");

    // 分配大量内存
    void **ptrs = malloc(sizeof(void*) * 1000);
    int success_count = 0;

    for (int i = 0; i < 1000; i++) {
        ptrs[i] = kmalloc(1024);
        if (ptrs[i]) {
            memset(ptrs[i], i & 0xFF, 1024);
            success_count++;
        }
    }

    printf("成功分配 %d/1000 个1KB对象\n", success_count);

    // 释放所有内存
    for (int i = 0; i < 1000; i++) {
        if (ptrs[i]) {
            kfree(ptrs[i]);
        }
    }
    free(ptrs);

    printf("✓ 内存泄漏检测完成\n");
}

/**
 * 运行压力测试
 */
static void run_stress_test(int test_type, int num_threads, int iterations) {
    pthread_t threads[MAX_THREADS];
    thread_data_t thread_data[MAX_THREADS];
    pthread_barrier_t barrier;
    double start_time, end_time;

    // 初始化统计
    memset(thread_stats, 0, sizeof(thread_stats));
    total_allocations = 0;
    total_frees = 0;
    allocation_errors = 0;
    free_errors = 0;

    // 初始化屏障
    pthread_barrier_init(&barrier, NULL, num_threads);

    printf("\n=== 压力测试 %d ===\n", test_type);
    printf("线程数: %d, 每线程迭代: %d\n", num_threads, iterations);

    start_time = get_time_ms();

    // 创建线程
    for (int i = 0; i < num_threads; i++) {
        thread_data[i].thread_id = i;
        thread_data[i].iterations = iterations;
        thread_data[i].max_concurrent_allocs = 1000;
        thread_data[i].barrier = &barrier;
        thread_data[i].allocations = NULL;

        void *(*thread_func)(void*) = NULL;

        switch (test_type) {
            case 1:
                thread_func = basic_alloc_thread;
                break;
            case 2:
                thread_func = memory_hold_thread;
                break;
            case 3:
                thread_func = fragmentation_thread;
                break;
            default:
                thread_func = basic_alloc_thread;
                break;
        }

        if (pthread_create(&threads[i], NULL, thread_func, &thread_data[i]) != 0) {
            printf("创建线程 %d 失败\n", i);
            return;
        }
    }

    // 等待所有线程完成
    for (int i = 0; i < num_threads; i++) {
        pthread_join(threads[i], NULL);
    }

    end_time = get_time_ms();

    // 销毁屏障
    pthread_barrier_destroy(&barrier);

    // 打印统计结果
    printf("\n--- 测试结果 ---\n");
    printf("总测试时间: %.2f ms\n", end_time - start_time);
    printf("总分配次数: %d\n", total_allocations);
    printf("总释放次数: %d\n", total_frees);
    printf("分配错误: %d\n", allocation_errors);
    printf("释放错误: %d\n", free_errors);

    if (total_allocations > 0) {
        printf("分配成功率: %.2f%%\n",
               (double)(total_allocations - allocation_errors) / total_allocations * 100);
    }

    if (total_frees > 0) {
        printf("释放成功率: %.2f%%\n",
               (double)(total_frees - free_errors) / total_frees * 100);
    }

    // 打印每个线程的统计
    printf("\n--- 线程统计 ---\n");
    for (int i = 0; i < num_threads; i++) {
        printf("线程 %2d: 分配 %8d, 释放 %8d, 错误 %6d, 时间 %8.2f ms, 最大内存 %6zu KB\n",
               i, thread_stats[i].allocations, thread_stats[i].frees,
               thread_stats[i].errors, thread_stats[i].total_time,
               thread_stats[i].max_memory_used / 1024);
    }
}

/**
 * 打印使用说明
 */
static void print_usage(const char *program_name) {
    printf("SLUB分配器压力测试程序\n");
    printf("用法: %s [选项]\n", program_name);
    printf("\n选项:\n");
    printf("  -t <类型>    测试类型 (1=基础, 2=内存保持, 3=碎片化, 0=全部)\n");
    printf("  -n <线程数>  并发线程数 (1-%d)\n", MAX_THREADS);
    printf("  -i <迭代数>  每线程迭代次数\n");
    printf("  -d <秒数>    测试持续时间（秒）\n");
    printf("  -h           显示此帮助信息\n");
    printf("\n示例:\n");
    printf("  %s -t 1 -n 8 -i 10000    # 8线程基础测试，每线程10000次迭代\n", program_name);
    printf("  %s -t 0 -n 4 -d 60       # 全部测试，4线程，持续60秒\n", program_name);
}

/**
 * 主函数
 */
int main(int argc, char *argv[]) {
    int test_type = 0;  // 0 = 全部测试
    int num_threads = 4;
    int iterations = 5000;
    // int duration = 0;   // 0 = 使用迭代次数 (暂未使用)

    // 解析命令行参数
    for (int i = 1; i < argc; i++) {
        if (strcmp(argv[i], "-t") == 0 && i + 1 < argc) {
            test_type = atoi(argv[++i]);
        } else if (strcmp(argv[i], "-n") == 0 && i + 1 < argc) {
            num_threads = atoi(argv[++i]);
            if (num_threads < 1 || num_threads > MAX_THREADS) {
                printf("线程数必须在1-%d之间\n", MAX_THREADS);
                return 1;
            }
        } else if (strcmp(argv[i], "-i") == 0 && i + 1 < argc) {
            iterations = atoi(argv[++i]);
        } else if (strcmp(argv[i], "-d") == 0 && i + 1 < argc) {
            // duration = atoi(argv[++i]); // 暂未实现时间控制
            i++; // 跳过参数
        } else if (strcmp(argv[i], "-h") == 0) {
            print_usage(argv[0]);
            return 0;
        }
    }

    printf("SLUB分配器压力测试程序\n");
    printf("======================\n");

    // 设置信号处理
    signal(SIGINT, signal_handler);
    signal(SIGTERM, signal_handler);

    // 初始化随机数种子
    srand((unsigned int)time(NULL));

    // 初始化SLUB分配器
    printf("初始化SLUB分配器...\n");
    if (slub_init() != 0) {
        printf("SLUB分配器初始化失败\n");
        return 1;
    }

    // 运行边界条件测试
    boundary_test();

    // 运行内存泄漏检测测试
    memory_leak_test();

    // 运行压力测试
    if (test_type == 0) {
        // 运行所有测试
        for (int t = 1; t <= 3; t++) {
            run_stress_test(t, num_threads, iterations);
            if (!test_running) break;
        }
    } else {
        // 运行指定测试
        run_stress_test(test_type, num_threads, iterations);
    }

    // 打印最终统计
    printf("\n");
    slub_print_stats();

    // 清理SLUB分配器
    printf("\n清理SLUB分配器...\n");
    slub_cleanup();

    printf("\n压力测试完成！\n");
    return 0;
}
