/**
 * SLUB分配器边界大小测试程序
 * 
 * 测试各种边界大小的内存分配，验证缓存选择的正确性
 * 特别关注跨越缓存边界的大小，如65、127、129等
 */

#include "slub.h"
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <assert.h>

/**
 * 测试结构体
 */
typedef struct {
    size_t size;
    size_t expected_cache_size;
    const char *description;
} boundary_test_t;

/**
 * 边界测试用例
 */
static const boundary_test_t boundary_tests[] = {
    // 8字节缓存边界
    {1, 8, "最小分配"},
    {7, 8, "8字节缓存内最大"},
    {8, 8, "8字节缓存边界"},
    
    // 16字节缓存边界
    {9, 16, "跨越8字节边界"},
    {15, 16, "16字节缓存内最大"},
    {16, 16, "16字节缓存边界"},
    
    // 32字节缓存边界
    {17, 32, "跨越16字节边界"},
    {31, 32, "32字节缓存内最大"},
    {32, 32, "32字节缓存边界"},
    
    // 64字节缓存边界
    {33, 64, "跨越32字节边界"},
    {63, 64, "64字节缓存内最大"},
    {64, 64, "64字节缓存边界"},
    
    // 128字节缓存边界（重点测试）
    {65, 128, "跨越64字节边界（关键测试）"},
    {96, 128, "128字节缓存中间值"},
    {127, 128, "128字节缓存内最大（关键测试）"},
    {128, 128, "128字节缓存边界"},
    
    // 256字节缓存边界
    {129, 256, "跨越128字节边界（关键测试）"},
    {192, 256, "256字节缓存中间值"},
    {255, 256, "256字节缓存内最大"},
    {256, 256, "256字节缓存边界"},
    
    // 512字节缓存边界
    {257, 512, "跨越256字节边界"},
    {384, 512, "512字节缓存中间值"},
    {511, 512, "512字节缓存内最大"},
    {512, 512, "512字节缓存边界"},
    
    // 1024字节缓存边界
    {513, 1024, "跨越512字节边界"},
    {768, 1024, "1024字节缓存中间值"},
    {1023, 1024, "1024字节缓存内最大"},
    {1024, 1024, "1024字节缓存边界"},
    
    // 2048字节缓存边界
    {1025, 2048, "跨越1024字节边界"},
    {1536, 2048, "2048字节缓存中间值"},
    {2047, 2048, "2048字节缓存内最大"},
    {2048, 2048, "2048字节缓存边界（SLUB最大）"},
};

static const int num_boundary_tests = sizeof(boundary_tests) / sizeof(boundary_tests[0]);

/**
 * 验证内存内容
 */
static int verify_memory_pattern(void *ptr, size_t size, unsigned char pattern) {
    unsigned char *data = (unsigned char *)ptr;
    for (size_t i = 0; i < size; i++) {
        if (data[i] != pattern) {
            printf("    ✗ 内存验证失败：位置 %zu，期望 0x%02X，实际 0x%02X\n", 
                   i, pattern, data[i]);
            return 0;
        }
    }
    return 1;
}

/**
 * 单个边界大小测试
 */
static int test_boundary_size(const boundary_test_t *test) {
    printf("测试 %zu 字节分配 (%s)...\n", test->size, test->description);
    
    // 分配内存
    void *ptr = kmalloc(test->size);
    if (!ptr) {
        printf("    ✗ 分配失败\n");
        return 0;
    }
    
    printf("    ✓ 分配成功，地址: %p\n", ptr);
    
    // 写入测试模式
    unsigned char pattern = (unsigned char)(test->size & 0xFF);
    memset(ptr, pattern, test->size);
    printf("    ✓ 写入测试模式 0x%02X\n", pattern);
    
    // 验证内存内容
    if (!verify_memory_pattern(ptr, test->size, pattern)) {
        kfree(ptr);
        return 0;
    }
    printf("    ✓ 内存验证通过\n");
    
    // 释放内存
    kfree(ptr);
    printf("    ✓ 释放成功\n");
    printf("    期望使用缓存: kmalloc-%zu\n", test->expected_cache_size);
    
    return 1;
}

/**
 * 批量分配测试
 */
static int test_batch_allocation(void) {
    printf("\n=== 批量边界大小分配测试 ===\n");
    
    void *ptrs[num_boundary_tests];
    int success_count = 0;
    
    // 批量分配
    printf("批量分配 %d 个不同大小的对象...\n", num_boundary_tests);
    for (int i = 0; i < num_boundary_tests; i++) {
        ptrs[i] = kmalloc(boundary_tests[i].size);
        if (ptrs[i]) {
            // 写入唯一标识
            unsigned char pattern = (unsigned char)(i + 1);
            memset(ptrs[i], pattern, boundary_tests[i].size);
            success_count++;
        } else {
            printf("    ✗ 分配 %zu 字节失败\n", boundary_tests[i].size);
        }
    }
    
    printf("成功分配: %d/%d\n", success_count, num_boundary_tests);
    
    // 验证所有分配的内存
    printf("验证所有分配的内存...\n");
    int verify_count = 0;
    for (int i = 0; i < num_boundary_tests; i++) {
        if (ptrs[i]) {
            unsigned char expected_pattern = (unsigned char)(i + 1);
            if (verify_memory_pattern(ptrs[i], boundary_tests[i].size, expected_pattern)) {
                verify_count++;
            } else {
                printf("    ✗ 对象 %d (%zu 字节) 验证失败\n", 
                       i, boundary_tests[i].size);
            }
        }
    }
    
    printf("验证通过: %d/%d\n", verify_count, success_count);
    
    // 批量释放
    printf("批量释放所有对象...\n");
    for (int i = 0; i < num_boundary_tests; i++) {
        if (ptrs[i]) {
            kfree(ptrs[i]);
        }
    }
    
    printf("✓ 批量测试完成\n");
    return (success_count == num_boundary_tests && verify_count == success_count);
}

/**
 * 重复分配释放测试
 */
static int test_repeated_allocation(void) {
    printf("\n=== 重复分配释放测试 ===\n");
    
    const size_t test_sizes[] = {65, 127, 129, 255, 257, 511, 513, 1023, 1025, 2047};
    const int num_sizes = sizeof(test_sizes) / sizeof(test_sizes[0]);
    const int iterations = 100;
    
    for (int i = 0; i < num_sizes; i++) {
        size_t size = test_sizes[i];
        printf("测试 %zu 字节，重复 %d 次...\n", size, iterations);
        
        int success_count = 0;
        for (int j = 0; j < iterations; j++) {
            void *ptr = kmalloc(size);
            if (ptr) {
                // 写入测试模式
                unsigned char pattern = (unsigned char)(j & 0xFF);
                memset(ptr, pattern, size);
                
                // 验证
                if (verify_memory_pattern(ptr, size, pattern)) {
                    success_count++;
                }
                
                kfree(ptr);
            }
        }
        
        printf("    成功: %d/%d (%.1f%%)\n", 
               success_count, iterations, 
               (double)success_count / iterations * 100.0);
        
        if (success_count != iterations) {
            printf("    ✗ 重复测试失败\n");
            return 0;
        }
    }
    
    printf("✓ 所有重复测试通过\n");
    return 1;
}

/**
 * 主函数
 */
int main(void) {
    printf("SLUB分配器边界大小测试程序\n");
    printf("============================\n\n");
    
    // 初始化SLUB分配器
    printf("初始化SLUB分配器...\n");
    if (slub_init() != 0) {
        printf("SLUB分配器初始化失败\n");
        return 1;
    }
    printf("✓ SLUB分配器初始化成功\n\n");
    
    int total_tests = 0;
    int passed_tests = 0;
    
    // 单个边界大小测试
    printf("=== 单个边界大小测试 ===\n");
    for (int i = 0; i < num_boundary_tests; i++) {
        total_tests++;
        if (test_boundary_size(&boundary_tests[i])) {
            passed_tests++;
            printf("    ✓ 测试通过\n");
        } else {
            printf("    ✗ 测试失败\n");
        }
        printf("\n");
    }
    
    // 批量分配测试
    total_tests++;
    if (test_batch_allocation()) {
        passed_tests++;
    }
    
    // 重复分配释放测试
    total_tests++;
    if (test_repeated_allocation()) {
        passed_tests++;
    }
    
    // 打印最终统计
    printf("\n=== 测试结果汇总 ===\n");
    printf("总测试数: %d\n", total_tests);
    printf("通过测试: %d\n", passed_tests);
    printf("失败测试: %d\n", total_tests - passed_tests);
    printf("成功率: %.1f%%\n", (double)passed_tests / total_tests * 100.0);
    
    if (passed_tests == total_tests) {
        printf("🎉 所有边界大小测试通过！\n");
    } else {
        printf("❌ 部分测试失败\n");
    }
    
    // 打印SLUB统计信息
    printf("\n");
    slub_print_stats();
    
    // 清理SLUB分配器
    printf("\n清理SLUB分配器...\n");
    slub_cleanup();
    
    return (passed_tests == total_tests) ? 0 : 1;
}
