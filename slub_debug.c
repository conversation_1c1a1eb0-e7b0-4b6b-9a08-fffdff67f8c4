#include "slub.h"
#include <stdio.h>
#include <time.h>

/* 外部函数声明 */
extern void page_allocator_print_stats(void);
extern void cpu_support_print_stats(void);
extern void get_percpu_stats(struct kmem_cache *cache, int cpu_id, 
                           int *freelist_count, bool *has_page);

/**
 * 打印页的详细信息
 */
void slub_debug_print_page(struct page *page) {
    if (!page) {
        printf("页指针为NULL\n");
        return;
    }
    
    printf("\n--- 页信息 (ID: %llu) ---\n", (unsigned long long)page->page_id);
    printf("虚拟地址: %p\n", page->s_mem);
    printf("标志位: 0x%lx", page->flags);
    if (page->flags & PG_slab) printf(" [SLAB]");
    if (page->flags & PG_active) printf(" [ACTIVE]");
    printf("\n");
    
    if (page->slab_cache) {
        printf("所属缓存: %s\n", page->slab_cache->name);
        printf("对象大小: %u 字节\n", page->slab_cache->size);
    } else {
        printf("所属缓存: 无\n");
    }
    
    printf("总对象数: %u\n", page->objects);
    printf("使用中对象: %u\n", page->inuse);
    printf("空闲对象: %u\n", page->objects - page->inuse);
    printf("空闲链表头: %p\n", page->freelist);
    printf("下一页: %p\n", page->next);
    
    /* 计算内存使用率 */
    if (page->objects > 0) {
        double usage = (double)page->inuse * 100.0 / page->objects;
        printf("使用率: %.1f%%\n", usage);
    }
}

/**
 * 打印缓存的详细信息
 */
void slub_debug_print_cache(struct kmem_cache *cache) {
    if (!cache) {
        printf("缓存指针为NULL\n");
        return;
    }
    
    printf("\n=== 缓存信息: %s ===\n", cache->name);
    printf("对象大小: %u 字节 (实际: %u 字节)\n", 
           cache->size, cache->object_size);
    printf("每页对象数: %u\n", cache->objects_per_page);
    printf("偏移量: %u\n", cache->offset);
    printf("标志位: 0x%x\n", cache->flags);
    printf("使用页数: %lu\n", cache->page_count);
    
    /* 统计信息 */
    printf("\n--- 统计信息 ---\n");
    printf("分配次数: %lu\n", cache->alloc_count);
    printf("释放次数: %lu\n", cache->free_count);
    printf("当前分配对象: %lu\n", cache->alloc_count - cache->free_count);
    
    if (cache->alloc_count > 0) {
        double hit_rate = (double)cache->free_count * 100.0 / cache->alloc_count;
        printf("释放率: %.1f%%\n", hit_rate);
    }
    
    /* Per-CPU缓存信息 */
    printf("\n--- Per-CPU缓存状态 ---\n");
    for (int i = 0; i < MAX_CPUS; i++) {
        int freelist_count = 0;
        bool has_page = false;
        get_percpu_stats(cache, i, &freelist_count, &has_page);
        
        if (freelist_count > 0 || has_page) {
            printf("CPU %d: freelist=%d对象, page=%s\n", 
                   i, freelist_count, has_page ? "有" : "无");
        }
    }
    
    /* 部分使用页链表 */
    printf("\n--- 部分使用页链表 ---\n");
    struct page *page = cache->partial_list;
    int partial_count = 0;
    int total_partial_objects = 0;
    int used_partial_objects = 0;
    
    while (page && partial_count < 10) {  /* 最多显示10页 */
        printf("页 %llu: %u/%u 对象使用中\n",
               (unsigned long long)page->page_id, page->inuse, page->objects);
        total_partial_objects += page->objects;
        used_partial_objects += page->inuse;
        page = page->next;
        partial_count++;
    }
    
    if (partial_count > 0) {
        printf("部分使用页数: %d\n", partial_count);
        printf("部分页总对象: %d\n", total_partial_objects);
        printf("部分页使用对象: %d\n", used_partial_objects);
        if (total_partial_objects > 0) {
            double partial_usage = (double)used_partial_objects * 100.0 / total_partial_objects;
            printf("部分页使用率: %.1f%%\n", partial_usage);
        }
    } else {
        printf("无部分使用页\n");
    }
    
    if (page) {
        printf("... (还有更多页)\n");
    }
}

/**
 * 打印所有缓存的统计信息
 */
void slub_print_stats(void) {
    if (!g_slub.initialized) {
        printf("SLUB分配器未初始化\n");
        return;
    }
    
    printf("\n");
    printf("╔══════════════════════════════════════════════════════════════╗\n");
    printf("║                    SLUB分配器统计信息                        ║\n");
    printf("╚══════════════════════════════════════════════════════════════╝\n");
    
    /* 全局信息 */
    int total_caches, total_pages;
    size_t total_memory;
    slub_get_info(&total_caches, &total_pages, &total_memory);
    
    printf("\n--- 全局统计 ---\n");
    printf("总缓存数: %d\n", total_caches);
    printf("使用页数: %d\n", total_pages);
    printf("使用内存: %zu KB (%.1f MB)\n", 
           total_memory / 1024, (double)total_memory / (1024 * 1024));
    
    /* 遍历所有缓存 */
    printf("\n--- 缓存详情 ---\n");
    pthread_mutex_lock(&g_slub.cache_lock);
    struct kmem_cache *cache = g_slub.cache_list;
    int cache_index = 0;
    
    while (cache) {
        printf("\n[%d] 缓存: %-20s | 大小: %4u | 页数: %3lu | 分配: %8lu | 释放: %8lu\n",
               cache_index++, cache->name, cache->size, cache->page_count,
               cache->alloc_count, cache->free_count);
        
        /* 计算内存使用 */
        size_t cache_memory = cache->page_count * PAGE_SIZE;
        size_t object_memory = (cache->alloc_count - cache->free_count) * cache->size;
        if (cache_memory > 0) {
            double efficiency = (double)object_memory * 100.0 / cache_memory;
            printf("    内存: %zu KB, 对象内存: %zu KB, 效率: %.1f%%\n",
                   cache_memory / 1024, object_memory / 1024, efficiency);
        }
        
        cache = cache->next;
    }
    pthread_mutex_unlock(&g_slub.cache_lock);
    
    /* 页分配器统计 */
    page_allocator_print_stats();
    
    /* CPU支持统计 */
    cpu_support_print_stats();
    
    printf("\n");
}

/**
 * 打印内存碎片信息
 */
void slub_print_fragmentation(void) {
    if (!g_slub.initialized) {
        printf("SLUB分配器未初始化\n");
        return;
    }
    
    printf("\n=== 内存碎片分析 ===\n");
    
    size_t total_allocated_memory = 0;
    size_t total_used_memory = 0;
    size_t total_wasted_memory = 0;
    int total_pages = 0;
    int total_partial_pages = 0;
    
    pthread_mutex_lock(&g_slub.cache_lock);
    struct kmem_cache *cache = g_slub.cache_list;
    
    while (cache) {
        size_t cache_allocated = cache->page_count * PAGE_SIZE;
        size_t cache_used = (cache->alloc_count - cache->free_count) * cache->size;
        size_t cache_wasted = cache_allocated - cache_used;
        
        total_allocated_memory += cache_allocated;
        total_used_memory += cache_used;
        total_wasted_memory += cache_wasted;
        total_pages += cache->page_count;
        
        /* 统计部分使用页 */
        struct page *page = cache->partial_list;
        int partial_pages = 0;
        while (page) {
            partial_pages++;
            page = page->next;
        }
        total_partial_pages += partial_pages;
        
        if (cache->page_count > 0) {
            double fragmentation = (double)cache_wasted * 100.0 / cache_allocated;
            printf("缓存 %-20s: 碎片率 %5.1f%%, 部分页 %3d\n",
                   cache->name, fragmentation, partial_pages);
        }
        
        cache = cache->next;
    }
    pthread_mutex_unlock(&g_slub.cache_lock);
    
    printf("\n--- 总体碎片情况 ---\n");
    printf("总分配内存: %zu KB\n", total_allocated_memory / 1024);
    printf("实际使用内存: %zu KB\n", total_used_memory / 1024);
    printf("浪费内存: %zu KB\n", total_wasted_memory / 1024);
    printf("总页数: %d\n", total_pages);
    printf("部分使用页数: %d\n", total_partial_pages);
    
    if (total_allocated_memory > 0) {
        double overall_fragmentation = (double)total_wasted_memory * 100.0 / total_allocated_memory;
        printf("总体碎片率: %.1f%%\n", overall_fragmentation);
    }
    
    if (total_pages > 0) {
        double partial_ratio = (double)total_partial_pages * 100.0 / total_pages;
        printf("部分页比例: %.1f%%\n", partial_ratio);
    }
}

/**
 * 验证内存完整性
 */
bool slub_verify_integrity(void) {
    if (!g_slub.initialized) {
        printf("SLUB分配器未初始化\n");
        return false;
    }
    
    printf("\n=== 内存完整性检查 ===\n");
    bool integrity_ok = true;
    
    pthread_mutex_lock(&g_slub.cache_lock);
    struct kmem_cache *cache = g_slub.cache_list;
    
    while (cache) {
        printf("检查缓存: %s\n", cache->name);
        
        /* 检查分配/释放计数 */
        if (cache->free_count > cache->alloc_count) {
            printf("  错误：释放次数(%lu) > 分配次数(%lu)\n", 
                   cache->free_count, cache->alloc_count);
            integrity_ok = false;
        }
        
        /* 检查页链表 */
        struct page *page = cache->partial_list;
        int page_count = 0;
        while (page && page_count < 1000) {  /* 防止无限循环 */
            if (page->slab_cache != cache) {
                printf("  错误：页 %llu 的缓存指针不匹配\n", (unsigned long long)page->page_id);
                integrity_ok = false;
            }
            
            if (page->inuse > page->objects) {
                printf("  错误：页 %llu 使用对象数(%u) > 总对象数(%u)\n",
                       (unsigned long long)page->page_id, page->inuse, page->objects);
                integrity_ok = false;
            }
            
            page = page->next;
            page_count++;
        }
        
        if (page_count >= 1000) {
            printf("  警告：页链表可能存在循环\n");
            integrity_ok = false;
        }
        
        cache = cache->next;
    }
    pthread_mutex_unlock(&g_slub.cache_lock);
    
    if (integrity_ok) {
        printf("内存完整性检查通过\n");
    } else {
        printf("内存完整性检查失败！\n");
    }
    
    return integrity_ok;
}

/**
 * 导出调试信息到文件
 */
void slub_export_debug_info(const char *filename) {
    FILE *fp = fopen(filename, "w");
    if (!fp) {
        printf("无法创建调试文件: %s\n", filename);
        return;
    }
    
    /* 重定向stdout到文件 */
    FILE *old_stdout = stdout;
    stdout = fp;
    
    /* 输出时间戳 */
    time_t now = time(NULL);
    printf("SLUB调试信息导出时间: %s\n", ctime(&now));
    
    /* 输出所有统计信息 */
    slub_print_stats();
    slub_print_fragmentation();
    slub_verify_integrity();
    
    /* 恢复stdout */
    stdout = old_stdout;
    fclose(fp);
    
    printf("调试信息已导出到: %s\n", filename);
}
