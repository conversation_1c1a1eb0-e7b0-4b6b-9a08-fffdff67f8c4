#include "slub.h"
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <pthread.h>
#include <time.h>
#include <unistd.h>
#include <assert.h>

/* 测试配置 */
#define TEST_THREADS 8
#define TEST_ITERATIONS 1000
#define TEST_OBJECTS_PER_THREAD 100

/* 测试结果统计 */
struct test_stats {
    unsigned long alloc_count;
    unsigned long free_count;
    unsigned long alloc_time_ns;
    unsigned long free_time_ns;
    unsigned long errors;
};

/* 线程测试参数 */
struct thread_test_param {
    int thread_id;
    int iterations;
    int objects_per_iteration;
    struct test_stats stats;
};

/**
 * 获取纳秒时间戳
 */
static unsigned long get_time_ns(void) {
    struct timespec ts;
    clock_gettime(CLOCK_MONOTONIC, &ts);
    return ts.tv_sec * 1000000000UL + ts.tv_nsec;
}

/**
 * 基本功能测试
 */
static bool test_basic_functionality(void) {
    printf("\n=== 基本功能测试 ===\n");
    
    /* 测试kmalloc/kfree */
    printf("测试kmalloc/kfree...\n");
    
    void *ptrs[100];
    size_t sizes[] = {8, 16, 32, 64, 128, 256, 512, 1024, 2048, 4096};
    int size_count = sizeof(sizes) / sizeof(sizes[0]);
    
    /* 分配不同大小的内存 */
    for (int i = 0; i < 100; i++) {
        size_t size = sizes[i % size_count];
        ptrs[i] = kmalloc(size);
        
        if (!ptrs[i]) {
            printf("  错误：kmalloc(%zu) 失败\n", size);
            return false;
        }
        
        /* 写入测试数据 */
        memset(ptrs[i], 0xAA, size);
        
        /* 验证数据 */
        unsigned char *data = (unsigned char *)ptrs[i];
        for (size_t j = 0; j < size; j++) {
            if (data[j] != 0xAA) {
                printf("  错误：内存数据不正确\n");
                return false;
            }
        }
    }
    
    /* 释放内存 */
    for (int i = 0; i < 100; i++) {
        kfree(ptrs[i]);
    }
    
    printf("  kmalloc/kfree 测试通过\n");
    
    /* 测试缓存创建和销毁 */
    printf("测试缓存管理...\n");
    
    struct kmem_cache *test_cache = kmem_cache_create("test-cache", 128, 0, 0);
    if (!test_cache) {
        printf("  错误：无法创建测试缓存\n");
        return false;
    }
    
    /* 从缓存分配对象 */
    void *obj1 = kmem_cache_alloc(test_cache);
    void *obj2 = kmem_cache_alloc(test_cache);
    
    if (!obj1 || !obj2) {
        printf("  错误：缓存分配失败\n");
        kmem_cache_destroy(test_cache);
        return false;
    }
    
    /* 释放对象 */
    kmem_cache_free(test_cache, obj1);
    kmem_cache_free(test_cache, obj2);
    
    /* 销毁缓存 */
    kmem_cache_destroy(test_cache);
    
    printf("  缓存管理测试通过\n");
    
    return true;
}

/**
 * 多线程测试函数
 */
static void *thread_test_func(void *arg) {
    struct thread_test_param *param = (struct thread_test_param *)arg;
    void **objects = malloc(param->objects_per_iteration * sizeof(void *));
    
    if (!objects) {
        param->stats.errors++;
        return NULL;
    }
    
    printf("线程 %d 开始测试，迭代次数: %d\n", param->thread_id, param->iterations);
    
    for (int iter = 0; iter < param->iterations; iter++) {
        /* 分配阶段 */
        unsigned long start_time = get_time_ns();
        
        for (int i = 0; i < param->objects_per_iteration; i++) {
            size_t size = 32 + (rand() % 512);  /* 32-544字节随机大小 */
            objects[i] = kmalloc(size);
            
            if (!objects[i]) {
                param->stats.errors++;
                continue;
            }
            
            param->stats.alloc_count++;
            
            /* 写入测试数据 */
            memset(objects[i], 0x55, size);
        }
        
        unsigned long alloc_end = get_time_ns();
        param->stats.alloc_time_ns += (alloc_end - start_time);
        
        /* 验证数据完整性 */
        for (int i = 0; i < param->objects_per_iteration; i++) {
            if (objects[i]) {
                unsigned char *data = (unsigned char *)objects[i];
                /* 简单验证前几个字节 */
                if (data[0] != 0x55 || data[1] != 0x55) {
                    param->stats.errors++;
                }
            }
        }
        
        /* 释放阶段 */
        unsigned long free_start = get_time_ns();
        
        for (int i = 0; i < param->objects_per_iteration; i++) {
            if (objects[i]) {
                kfree(objects[i]);
                param->stats.free_count++;
            }
        }
        
        unsigned long free_end = get_time_ns();
        param->stats.free_time_ns += (free_end - free_start);
        
        /* 每100次迭代打印进度 */
        if ((iter + 1) % 100 == 0) {
            printf("线程 %d 完成 %d/%d 迭代\n", 
                   param->thread_id, iter + 1, param->iterations);
        }
    }
    
    free(objects);
    printf("线程 %d 测试完成\n", param->thread_id);
    return NULL;
}

/**
 * 多线程压力测试
 */
static bool test_multithreaded(void) {
    printf("\n=== 多线程压力测试 ===\n");
    printf("线程数: %d, 每线程迭代: %d, 每次分配对象: %d\n",
           TEST_THREADS, TEST_ITERATIONS, TEST_OBJECTS_PER_THREAD);
    
    pthread_t threads[TEST_THREADS];
    struct thread_test_param params[TEST_THREADS];
    
    /* 初始化参数 */
    for (int i = 0; i < TEST_THREADS; i++) {
        params[i].thread_id = i;
        params[i].iterations = TEST_ITERATIONS;
        params[i].objects_per_iteration = TEST_OBJECTS_PER_THREAD;
        memset(&params[i].stats, 0, sizeof(params[i].stats));
    }
    
    /* 启动线程 */
    unsigned long test_start = get_time_ns();
    
    for (int i = 0; i < TEST_THREADS; i++) {
        if (pthread_create(&threads[i], NULL, thread_test_func, &params[i]) != 0) {
            printf("错误：无法创建线程 %d\n", i);
            return false;
        }
    }
    
    /* 等待线程完成 */
    for (int i = 0; i < TEST_THREADS; i++) {
        pthread_join(threads[i], NULL);
    }
    
    unsigned long test_end = get_time_ns();
    unsigned long total_time = test_end - test_start;
    
    /* 统计结果 */
    struct test_stats total_stats = {0};
    for (int i = 0; i < TEST_THREADS; i++) {
        total_stats.alloc_count += params[i].stats.alloc_count;
        total_stats.free_count += params[i].stats.free_count;
        total_stats.alloc_time_ns += params[i].stats.alloc_time_ns;
        total_stats.free_time_ns += params[i].stats.free_time_ns;
        total_stats.errors += params[i].stats.errors;
    }
    
    printf("\n--- 测试结果 ---\n");
    printf("总分配次数: %lu\n", total_stats.alloc_count);
    printf("总释放次数: %lu\n", total_stats.free_count);
    printf("错误次数: %lu\n", total_stats.errors);
    printf("总测试时间: %.2f 秒\n", (double)total_time / 1000000000.0);
    
    if (total_stats.alloc_count > 0) {
        double avg_alloc_ns = (double)total_stats.alloc_time_ns / total_stats.alloc_count;
        printf("平均分配时间: %.0f 纳秒\n", avg_alloc_ns);
        printf("分配吞吐量: %.0f 次/秒\n", 
               (double)total_stats.alloc_count * 1000000000.0 / total_time);
    }
    
    if (total_stats.free_count > 0) {
        double avg_free_ns = (double)total_stats.free_time_ns / total_stats.free_count;
        printf("平均释放时间: %.0f 纳秒\n", avg_free_ns);
        printf("释放吞吐量: %.0f 次/秒\n", 
               (double)total_stats.free_count * 1000000000.0 / total_time);
    }
    
    return total_stats.errors == 0;
}

/**
 * 内存泄漏测试
 */
static bool test_memory_leak(void) {
    printf("\n=== 内存泄漏测试 ===\n");
    
    /* 记录初始状态 */
    int initial_caches, initial_pages;
    size_t initial_memory;
    slub_get_info(&initial_caches, &initial_pages, &initial_memory);
    
    printf("初始状态 - 缓存: %d, 页: %d, 内存: %zu KB\n",
           initial_caches, initial_pages, initial_memory / 1024);
    
    /* 执行大量分配和释放 */
    const int test_rounds = 10;
    const int objects_per_round = 1000;
    
    for (int round = 0; round < test_rounds; round++) {
        void **ptrs = malloc(objects_per_round * sizeof(void *));
        
        /* 分配 */
        for (int i = 0; i < objects_per_round; i++) {
            ptrs[i] = kmalloc(64 + (i % 256));
        }
        
        /* 释放 */
        for (int i = 0; i < objects_per_round; i++) {
            kfree(ptrs[i]);
        }
        
        free(ptrs);
        
        if ((round + 1) % 5 == 0) {
            printf("完成 %d/%d 轮测试\n", round + 1, test_rounds);
        }
    }
    
    /* 强制垃圾回收 */
    slub_gc();
    
    /* 检查最终状态 */
    int final_caches, final_pages;
    size_t final_memory;
    slub_get_info(&final_caches, &final_pages, &final_memory);
    
    printf("最终状态 - 缓存: %d, 页: %d, 内存: %zu KB\n",
           final_caches, final_pages, final_memory / 1024);
    
    /* 检查是否有明显的内存泄漏 */
    int page_diff = final_pages - initial_pages;
    size_t memory_diff = final_memory - initial_memory;
    
    printf("差异 - 页: %+d, 内存: %+ld KB\n", 
           page_diff, (long)memory_diff / 1024);
    
    /* 允许少量内存增长（缓存预分配等） */
    bool leak_ok = (page_diff <= 5) && (memory_diff <= 1024 * 1024);  /* 1MB容忍度 */
    
    if (leak_ok) {
        printf("内存泄漏测试通过\n");
    } else {
        printf("检测到可能的内存泄漏\n");
    }
    
    return leak_ok;
}

/**
 * 主测试函数
 */
int main(int argc, char *argv[]) {
    (void)argc;  /* 未使用的参数 */
    (void)argv;  /* 未使用的参数 */

    printf("SLUB内存分配器测试程序\n");
    printf("========================\n");
    
    /* 初始化随机数 */
    srand(time(NULL));
    
    /* 初始化SLUB分配器 */
    printf("初始化SLUB分配器...\n");
    if (slub_init() != 0) {
        printf("错误：SLUB分配器初始化失败\n");
        return 1;
    }
    
    bool all_tests_passed = true;
    
    /* 运行测试 */
    all_tests_passed &= test_basic_functionality();
    all_tests_passed &= test_multithreaded();
    all_tests_passed &= test_memory_leak();
    
    /* 打印统计信息 */
    slub_print_stats();
    
    /* 验证完整性 */
    printf("\n=== 完整性验证 ===\n");
    bool integrity_ok = slub_verify_integrity();
    all_tests_passed &= integrity_ok;
    
    /* 导出调试信息 */
    slub_export_debug_info("slub_debug.log");
    
    /* 清理 */
    printf("\n清理SLUB分配器...\n");
    slub_cleanup();
    
    /* 测试结果 */
    printf("\n");
    printf("========================\n");
    if (all_tests_passed) {
        printf("所有测试通过！✓\n");
        return 0;
    } else {
        printf("部分测试失败！✗\n");
        return 1;
    }
}
