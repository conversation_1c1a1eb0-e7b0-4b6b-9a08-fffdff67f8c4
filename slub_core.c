#include "slub.h"
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <errno.h>

/* 全局SLUB分配器实例 */
struct slub_allocator g_slub = {0};

/* 预定义的kmalloc缓存 */
struct kmem_cache *kmalloc_caches[13] = {0};

/* 缓存大小数组：8, 16, 32, 64, 128, 256, 512, 1024, 2048, 4096, 8192 */
static const size_t kmalloc_sizes[] = {
    8, 16, 32, 64, 128, 256, 512, 1024, 2048, 4096, 8192
};
static const int kmalloc_cache_count = sizeof(kmalloc_sizes) / sizeof(kmalloc_sizes[0]);

/**
 * 计算对象在页中的数量
 */
static unsigned int calculate_objects_per_page(size_t object_size) {
    /* 考虑对齐和元数据开销 */
    size_t usable_size = PAGE_SIZE - sizeof(void*);  /* 预留一些空间给元数据 */
    unsigned int objects = usable_size / object_size;

    /* 确保至少有一个对象，即使对于大对象 */
    if (objects == 0 && object_size <= PAGE_SIZE) {
        objects = 1;
    }

    return objects;
}

/**
 * 初始化页中的空闲对象链表
 */
static void init_page_freelist(struct page *page, struct kmem_cache *cache) {
    char *addr = (char *)page->s_mem;
    void *last = NULL;
    
    /* 从页末尾开始，向前构建链表 */
    for (int i = cache->objects_per_page - 1; i >= 0; i--) {
        void *obj = addr + i * cache->size;
        *(void **)obj = last;
        last = obj;
    }
    
    page->freelist = last;
    page->inuse = 0;
    page->objects = cache->objects_per_page;
    page->slab_cache = cache;
    page->flags |= PG_slab;
    
    printf("[SLUB_CORE] 初始化页 %llu，对象数量: %u，对象大小: %u\n",
           (unsigned long long)page->page_id, page->objects, cache->size);
}

/**
 * 从页中分配一个对象
 */
static void *alloc_from_page(struct page *page) {
    if (!page || !page->freelist) {
        return NULL;
    }
    
    void *object = page->freelist;
    page->freelist = *(void **)object;
    page->inuse++;
    
    /* 清零对象内容（可选，用于调试） */
    memset(object, 0, page->slab_cache->object_size);
    
    return object;
}

/**
 * 将对象释放回页
 */
static void free_to_page(struct page *page, void *object) {
    if (!page || !object) return;
    
    /* 将对象添加到freelist头部 */
    *(void **)object = page->freelist;
    page->freelist = object;
    page->inuse--;
    
    printf("[SLUB_CORE] 释放对象到页 %llu，使用中对象: %u/%u\n",
           (unsigned long long)page->page_id, page->inuse, page->objects);
}

/**
 * 为缓存分配新页
 */
static struct page *allocate_slab_page(struct kmem_cache *cache) {
    struct page *page = alloc_page();
    if (!page) {
        printf("[SLUB_CORE] 错误：无法分配新页给缓存 %s\n", cache->name);
        return NULL;
    }
    
    /* 初始化页作为slab */
    init_page_freelist(page, cache);
    
    /* 更新缓存统计 */
    cache->page_count++;
    
    printf("[SLUB_CORE] 为缓存 %s 分配新页 %llu\n", cache->name, (unsigned long long)page->page_id);
    return page;
}

/**
 * 释放slab页
 */
static void free_slab_page(struct kmem_cache *cache, struct page *page) {
    if (!page) return;
    
    printf("[SLUB_CORE] 释放缓存 %s 的页 %llu\n", cache->name, (unsigned long long)page->page_id);
    
    /* 清理页标志 */
    page->flags &= ~PG_slab;
    page->slab_cache = NULL;
    page->freelist = NULL;
    page->inuse = 0;
    page->objects = 0;
    
    /* 更新缓存统计 */
    cache->page_count--;
    
    /* 释放页 */
    free_page(page);
}

/**
 * 慢速路径：从部分使用的页或新页分配对象
 */
static void *alloc_slow_path(struct kmem_cache *cache) {
    struct page *page = NULL;
    void *object = NULL;
    int cpu_id = get_cpu_id();
    struct kmem_cache_cpu *cpu_cache = &cache->__percpu[cpu_id];
    
    spin_lock(&cpu_cache->lock);

    /* 1. 尝试从CPU的partial页分配 */
    if (cpu_cache->partial) {
        page = cpu_cache->partial;
        cpu_cache->partial = page->next;
        page->next = NULL;

        object = alloc_from_page(page);
        if (object) {
            cpu_cache->page = page;
            goto out;
        }
    }

    /* 2. 尝试从全局partial链表分配 */
    spin_lock(&cache->list_lock);
    if (cache->partial_list) {
        page = cache->partial_list;
        cache->partial_list = page->next;
        page->next = NULL;
        spin_unlock(&cache->list_lock);

        object = alloc_from_page(page);
        if (object) {
            cpu_cache->page = page;
            goto out;
        }
    } else {
        spin_unlock(&cache->list_lock);
    }

    /* 3. 分配新页 */
    page = allocate_slab_page(cache);
    if (page) {
        object = alloc_from_page(page);
        cpu_cache->page = page;
    }

out:
    spin_unlock(&cpu_cache->lock);
    
    if (object) {
        __sync_fetch_and_add(&cache->alloc_count, 1);  /* 原子增加 */
        printf("[SLUB_CORE] 慢速路径分配成功，缓存: %s，CPU: %d\n",
               cache->name, cpu_id);
    } else {
        printf("[SLUB_CORE] 慢速路径分配失败，缓存: %s\n", cache->name);
    }
    
    return object;
}

/**
 * 慢速路径：释放对象
 */
static void free_slow_path(struct kmem_cache *cache, void *object) {
    struct page *page = virt_to_page(object);
    if (!page) {
        printf("[SLUB_CORE] 错误：无法找到对象对应的页\n");
        return;
    }
    
    int cpu_id = get_cpu_id();
    struct kmem_cache_cpu *cpu_cache = &cache->__percpu[cpu_id];
    
    spin_lock(&cpu_cache->lock);

    /* 释放对象到页 */
    free_to_page(page, object);
    __sync_fetch_and_add(&cache->free_count, 1);  /* 原子增加 */

    /* 如果页变为空闲，考虑释放它 */
    if (page->inuse == 0) {
        /* 将页从任何链表中移除 */
        if (cpu_cache->page == page) {
            cpu_cache->page = NULL;
        }

        /* 释放空页（简化策略：立即释放） */
        spin_unlock(&cpu_cache->lock);
        free_slab_page(cache, page);
        return;
    }

    /* 如果页部分使用，将其添加到partial链表 */
    if (page->inuse < page->objects && cpu_cache->page != page) {
        spin_lock(&cache->list_lock);
        page->next = cache->partial_list;
        cache->partial_list = page;
        spin_unlock(&cache->list_lock);
    }

    spin_unlock(&cpu_cache->lock);
    
    printf("[SLUB_CORE] 慢速路径释放完成，缓存: %s，CPU: %d\n", 
           cache->name, cpu_id);
}

/**
 * 从缓存分配对象 - 主要接口
 */
void *kmem_cache_alloc(struct kmem_cache *cache) {
    if (!cache) return NULL;

    /* 快速路径：尝试从CPU缓存分配 */
    void *object = percpu_cache_alloc_fast(cache);
    if (object) {
        __sync_fetch_and_add(&cache->alloc_count, 1);  /* 原子增加 */
        return object;
    }

    /* 慢速路径 */
    return alloc_slow_path(cache);
}

/**
 * 释放对象到缓存 - 主要接口
 */
void kmem_cache_free(struct kmem_cache *cache, void *object) {
    if (!cache || !object) return;

    /* 快速路径：尝试释放到CPU缓存 */
    if (percpu_cache_free_fast(cache, object)) {
        __sync_fetch_and_add(&cache->free_count, 1);  /* 原子增加 */
        return;
    }

    /* 慢速路径 */
    free_slow_path(cache, object);
}

/**
 * 通用内存分配 - kmalloc实现
 */
void *kmalloc(size_t size) {
    if (size == 0) return NULL;
    
    /* 查找合适的缓存 */
    for (int i = 0; i < kmalloc_cache_count; i++) {
        if (size <= kmalloc_sizes[i]) {
            if (kmalloc_caches[i]) {
                return kmem_cache_alloc(kmalloc_caches[i]);
            }
            break;
        }
    }
    
    printf("[SLUB_CORE] 错误：无法找到大小 %zu 的合适缓存\n", size);
    return NULL;
}

/**
 * 通用内存释放 - kfree实现
 */
void kfree(void *ptr) {
    if (!ptr) return;
    
    /* 通过页找到对应的缓存 */
    struct page *page = virt_to_page(ptr);
    if (!page || !(page->flags & PG_slab)) {
        printf("[SLUB_CORE] 错误：无效的指针或非slab页\n");
        return;
    }
    
    struct kmem_cache *cache = page->slab_cache;
    if (!cache) {
        printf("[SLUB_CORE] 错误：页没有关联的缓存\n");
        return;
    }
    
    kmem_cache_free(cache, ptr);
}

/**
 * 创建新的kmem_cache
 */
struct kmem_cache *kmem_cache_create(const char *name, size_t size,
                                   size_t align, unsigned long flags) {
    (void)align;  /* 暂时未使用对齐参数 */

    if (!name || size == 0 || size > MAX_OBJECT_SIZE) {
        printf("[SLUB_CORE] 错误：无效的缓存参数\n");
        return NULL;
    }

    /* 分配缓存结构 */
    struct kmem_cache *cache = malloc(sizeof(struct kmem_cache));
    if (!cache) {
        printf("[SLUB_CORE] 错误：无法分配缓存结构\n");
        return NULL;
    }

    /* 初始化缓存 */
    memset(cache, 0, sizeof(struct kmem_cache));

    /* 设置基本属性 */
    strncpy(cache->name, name, sizeof(cache->name) - 1);
    cache->object_size = size;
    cache->size = (size + 7) & ~7;  /* 8字节对齐 */
    cache->flags = flags;
    cache->objects_per_page = calculate_objects_per_page(cache->size);

    /* 初始化per-CPU缓存 */
    for (int i = 0; i < MAX_CPUS; i++) {
        if (init_percpu_cache(&cache->__percpu[i]) != 0) {
            /* 清理已初始化的CPU缓存 */
            for (int j = 0; j < i; j++) {
                cleanup_percpu_cache(&cache->__percpu[j]);
            }
            free(cache);
            return NULL;
        }
    }

    /* 初始化自旋锁 */
    spin_lock_init(&cache->list_lock);

    /* 添加到全局缓存链表 */
    spin_lock(&g_slub.cache_lock);
    cache->next = g_slub.cache_list;
    g_slub.cache_list = cache;
    spin_unlock(&g_slub.cache_lock);

    printf("[SLUB_CORE] 创建缓存 '%s'，对象大小: %zu，每页对象数: %u\n",
           name, size, cache->objects_per_page);

    return cache;
}

/**
 * 销毁kmem_cache
 */
void kmem_cache_destroy(struct kmem_cache *cache) {
    if (!cache) return;

    printf("[SLUB_CORE] 销毁缓存 '%s'\n", cache->name);

    /* 从全局链表中移除 */
    spin_lock(&g_slub.cache_lock);
    struct kmem_cache **curr = &g_slub.cache_list;
    while (*curr) {
        if (*curr == cache) {
            *curr = cache->next;
            break;
        }
        curr = &(*curr)->next;
    }
    spin_unlock(&g_slub.cache_lock);

    /* 释放所有页 */
    struct page *page = cache->partial_list;
    while (page) {
        struct page *next = page->next;
        free_slab_page(cache, page);
        page = next;
    }

    /* 清理per-CPU缓存 */
    for (int i = 0; i < MAX_CPUS; i++) {
        struct kmem_cache_cpu *cpu_cache = &cache->__percpu[i];
        if (cpu_cache->page) {
            free_slab_page(cache, cpu_cache->page);
        }
        cleanup_percpu_cache(cpu_cache);
    }

    /* 自旋锁不需要显式销毁 */

    /* 释放缓存结构 */
    free(cache);
}

/**
 * 获取缓存的对象大小
 */
size_t kmem_cache_size(struct kmem_cache *cache) {
    return cache ? cache->object_size : 0;
}
