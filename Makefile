# SLUB内存分配器 Makefile
# 注意：此实现避免GPL协议，使用MIT/BSD风格许可

CC = gcc
CFLAGS = -Wall -Wextra -std=c99 -pthread -O2 -g
LDFLAGS = -pthread

# 目标文件
TARGET = test_slub
DEMO_TARGET = demo
MULTICORE_TARGET = multicore_test
LIB_TARGET = libslub.a

# 源文件
SOURCES = page_allocator.c cpu_support.c slub_core.c slub_init.c slub_debug.c
TEST_SOURCES = test_slub.c
DEMO_SOURCES = demo.c
MULTICORE_SOURCES = multicore_test.c
HEADERS = slub.h

# 对象文件
OBJECTS = $(SOURCES:.c=.o)
TEST_OBJECTS = $(TEST_SOURCES:.c=.o)
DEMO_OBJECTS = $(DEMO_SOURCES:.c=.o)
MULTICORE_OBJECTS = $(MULTICORE_SOURCES:.c=.o)

# 默认目标
all: $(TARGET) $(DEMO_TARGET) $(MULTICORE_TARGET) $(LIB_TARGET)

# 编译测试程序
$(TARGET): $(OBJECTS) $(TEST_OBJECTS)
	@echo "链接测试程序..."
	$(CC) $(OBJECTS) $(TEST_OBJECTS) -o $(TARGET) $(LDFLAGS)
	@echo "测试程序编译完成: $(TARGET)"

# 编译演示程序
$(DEMO_TARGET): $(OBJECTS) $(DEMO_OBJECTS)
	@echo "链接演示程序..."
	$(CC) $(OBJECTS) $(DEMO_OBJECTS) -o $(DEMO_TARGET) $(LDFLAGS)
	@echo "演示程序编译完成: $(DEMO_TARGET)"

# 编译多核测试程序
$(MULTICORE_TARGET): $(OBJECTS) $(MULTICORE_OBJECTS)
	@echo "链接多核测试程序..."
	$(CC) $(OBJECTS) $(MULTICORE_OBJECTS) -o $(MULTICORE_TARGET) $(LDFLAGS)
	@echo "多核测试程序编译完成: $(MULTICORE_TARGET)"

# 编译静态库
$(LIB_TARGET): $(OBJECTS)
	@echo "创建静态库..."
	ar rcs $(LIB_TARGET) $(OBJECTS)
	@echo "静态库创建完成: $(LIB_TARGET)"

# 编译对象文件
%.o: %.c $(HEADERS)
	@echo "编译 $<..."
	$(CC) $(CFLAGS) -c $< -o $@

# 运行测试
test: $(TARGET)
	@echo "运行SLUB分配器测试..."
	@echo "========================"
	./$(TARGET)

# 运行演示
demo: $(DEMO_TARGET)
	@echo "运行SLUB分配器演示..."
	@echo "========================"
	./$(DEMO_TARGET)

# 运行多核测试
multicore: $(MULTICORE_TARGET)
	@echo "运行SLUB多核性能测试..."
	@echo "========================"
	./$(MULTICORE_TARGET)

# 运行性能测试
perf: $(TARGET)
	@echo "运行性能测试..."
	@echo "使用perf工具分析性能（需要root权限）"
	sudo perf record -g ./$(TARGET)
	sudo perf report

# 运行内存检查
valgrind: $(TARGET)
	@echo "使用Valgrind检查内存错误..."
	valgrind --tool=memcheck --leak-check=full --show-leak-kinds=all ./$(TARGET)

# 运行地址消毒器版本
asan: clean
	@echo "编译地址消毒器版本..."
	$(MAKE) CFLAGS="$(CFLAGS) -fsanitize=address -fno-omit-frame-pointer" LDFLAGS="$(LDFLAGS) -fsanitize=address"
	@echo "运行地址消毒器测试..."
	./$(TARGET)

# 运行线程消毒器版本
tsan: clean
	@echo "编译线程消毒器版本..."
	$(MAKE) CFLAGS="$(CFLAGS) -fsanitize=thread" LDFLAGS="$(LDFLAGS) -fsanitize=thread"
	@echo "运行线程消毒器测试..."
	./$(TARGET)

# 生成调试版本
debug: clean
	@echo "编译调试版本..."
	$(MAKE) CFLAGS="$(CFLAGS) -DDEBUG -O0"

# 生成发布版本
release: clean
	@echo "编译发布版本..."
	$(MAKE) CFLAGS="-Wall -Wextra -std=c99 -pthread -O3 -DNDEBUG"

# 代码格式化
format:
	@echo "格式化代码..."
	@if command -v clang-format >/dev/null 2>&1; then \
		clang-format -i *.c *.h; \
		echo "代码格式化完成"; \
	else \
		echo "警告：未找到clang-format，跳过格式化"; \
	fi

# 静态分析
analyze:
	@echo "运行静态分析..."
	@if command -v cppcheck >/dev/null 2>&1; then \
		cppcheck --enable=all --std=c99 *.c; \
	else \
		echo "警告：未找到cppcheck，跳过静态分析"; \
	fi

# 生成文档
doc:
	@echo "生成文档..."
	@if command -v doxygen >/dev/null 2>&1; then \
		doxygen Doxyfile; \
		echo "文档生成完成，查看 doc/html/index.html"; \
	else \
		echo "警告：未找到doxygen，跳过文档生成"; \
	fi

# 清理
clean:
	@echo "清理编译文件..."
	rm -f $(OBJECTS) $(TEST_OBJECTS) $(DEMO_OBJECTS) $(MULTICORE_OBJECTS) $(TARGET) $(DEMO_TARGET) $(MULTICORE_TARGET) $(LIB_TARGET)
	rm -f *.log core.*
	rm -rf doc/

# 深度清理
distclean: clean
	@echo "深度清理..."
	rm -f perf.data*
	rm -rf .git/

# 安装（可选）
install: $(LIB_TARGET)
	@echo "安装SLUB库..."
	@mkdir -p /usr/local/lib
	@mkdir -p /usr/local/include
	@cp $(LIB_TARGET) /usr/local/lib/
	@cp $(HEADERS) /usr/local/include/
	@echo "安装完成"

# 卸载
uninstall:
	@echo "卸载SLUB库..."
	@rm -f /usr/local/lib/$(LIB_TARGET)
	@rm -f /usr/local/include/slub.h
	@echo "卸载完成"

# 创建发布包
dist: clean
	@echo "创建发布包..."
	@mkdir -p slub-allocator
	@cp *.c *.h Makefile README.md LICENSE slub-allocator/
	@tar czf slub-allocator.tar.gz slub-allocator/
	@rm -rf slub-allocator/
	@echo "发布包创建完成: slub-allocator.tar.gz"

# 显示帮助
help:
	@echo "SLUB内存分配器 Makefile"
	@echo "======================="
	@echo ""
	@echo "可用目标："
	@echo "  all      - 编译所有目标（默认）"
	@echo "  test     - 编译并运行测试"
	@echo "  perf     - 运行性能分析"
	@echo "  valgrind - 运行内存检查"
	@echo "  asan     - 编译并运行地址消毒器版本"
	@echo "  tsan     - 编译并运行线程消毒器版本"
	@echo "  debug    - 编译调试版本"
	@echo "  release  - 编译发布版本"
	@echo "  format   - 格式化代码"
	@echo "  analyze  - 运行静态分析"
	@echo "  doc      - 生成文档"
	@echo "  clean    - 清理编译文件"
	@echo "  install  - 安装库文件"
	@echo "  dist     - 创建发布包"
	@echo "  help     - 显示此帮助"

# 声明伪目标
.PHONY: all test perf valgrind asan tsan debug release format analyze doc clean distclean install uninstall dist help

# 显示编译信息
info:
	@echo "编译器: $(CC)"
	@echo "编译选项: $(CFLAGS)"
	@echo "链接选项: $(LDFLAGS)"
	@echo "源文件: $(SOURCES)"
	@echo "头文件: $(HEADERS)"
